<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/logo-hai.svg" />
    <title>HAI 海小合 - 地产行业AI合同服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#722ED1',
                        accent: '#00B4D8',
                        dark: '#1D2129',
                        light: '#F5F7FA',
                        neutral: {
                            100: '#F5F7FA',
                            200: '#E5E6EB',
                            300: '#C9CDD4',
                            400: '#86909C',
                            500: '#4E5969',
                            600: '#1D2129',
                        }
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                    boxShadow: {
                        'card': '0 10px 30px -5px rgba(0, 0, 0, 0.1)',
                        'card-hover': '0 20px 40px -5px rgba(0, 0, 0, 0.15)',
                    },
                    // 增加左右内边距，加大两侧留白
                    padding: {
                        'side': 'clamp(5rem, 10vw, 10rem)' 
                    }
                },
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .text-gradient {
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
            }
            .bg-glass {
                backdrop-filter: blur(12px);
                -webkit-backdrop-filter: blur(12px);
            }
            .transition-custom {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .scale-hover {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .scale-hover:hover {
                transform: translateY(-5px) scale(1.01);
            }
            .section-padding {
                padding-top: clamp(3rem, 8vh, 6rem);
                padding-bottom: clamp(3rem, 8vh, 6rem);
            }
            /* 优化卡片样式 */
            .optimized-card {
                background: #fff;
                border-radius: 20px;
                box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
                padding: 3rem;
                text-align: center;
                transition: all 0.3s ease;
            }
            .optimized-card:hover {
                box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.15);
                transform: translateY(-10px);
            }
            .optimized-card-icon {
                width: 64px;
                height: 64px;
                background: rgba(22, 93, 255, 0.1);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 2rem;
                transition: background 0.3s ease;
            }
            .optimized-card:hover .optimized-card-icon {
                background: rgba(22, 93, 255, 0.2);
            }
            /* AI赋能对比表格样式 */
            .ai-comparison {
                background: #ffffff;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
                margin-top: 4rem;
            }
            .ai-comparison-header {
                background: linear-gradient(90deg, rgba(22, 93, 255, 0.05), rgba(114, 46, 209, 0.05));
                padding: 2rem;
                text-align: center;
                position: relative;
                overflow: hidden;
            }
            /* 新增的背景图片样式 */
            .ai-comparison-header-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: url('https://picsum.photos/id/1048/1200/400');
                background-size: cover;
                background-position: center;
                z-index: 0;
            }
            .ai-comparison-header-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, rgba(22, 93, 255, 0.8), rgba(114, 46, 209, 0.8));
                z-index: 1;
            }
            .ai-comparison-header-content {
                position: relative;
                z-index: 2;
            }
            .ai-comparison-body {
                display: grid;
                grid-template-columns: 1fr 1fr;
            }
            .ai-comparison-item {
                padding: 3rem;
                border-right: 1px solid #E5E6EB;
            }
            .ai-comparison-item:last-child {
                border-right: none;
            }
            .ai-comparison-item h3 {
                font-size: 1.75rem; /* 增大标题字体 */
                font-weight: 600;
                margin-bottom: 2rem; /* 增加标题底部间距 */
                display: flex;
                align-items: center;
            }
            .ai-comparison-item h3::before {
                content: '';
                width: 10px; /* 增大标题前标记的宽度 */
                height: 30px; /* 增大标题前标记的高度 */
                border-radius: 5px;
                margin-right: 1.5rem; /* 增加标记与标题的间距 */
            }
            .ai-comparison-item.before h3::before {
                background: #FF4D4F;
            }
            .ai-comparison-item.after h3::before {
                background: #52C41A;
            }
            .ai-comparison-item ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }
            .ai-comparison-item ul li {
                margin-bottom: 1.5rem; /* 增加列表项底部间距 */
                padding-left: 2.5rem; /* 增加列表项左侧内边距 */
                position: relative;
                font-size: 1.1rem; /* 增大列表项字体 */
            }
            .ai-comparison-item.before ul li::before {
                content: '✖';
                color: #FF4D4F;
                position: absolute;
                left: 0;
            }
            .ai-comparison-item.after ul li::before {
                content: '✓';
                color: #52C41A;
                position: absolute;
                left: 0;
            }
        }
    </style>
</head>
<body class="font-inter text-neutral-600 bg-white overflow-x-hidden">
    <!-- 导航栏 -->
    <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-dark/80 bg-glass text-white">
        <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
            <div class="flex items-center justify-between h-16 md:h-20">
                <div class="flex items-center">
                    <div class="text-white font-bold text-xl md:text-2xl flex items-center">
                      <img src="../images/website/logo-dark.png" alt="背景" class="h-[40px] object-cover">
                    </div>
                </div>
                
                <!-- 桌面导航 -->
                <nav class="hidden md:flex space-x-8">
                    <a href="#features" class="text-white/80 hover:text-white transition-custom">产品服务</a>
                    <a href="#values" class="text-white/80 hover:text-white transition-custom">服务价值</a>
                    <a href="#advantages" class="text-white/80 hover:text-white transition-custom">服务优势</a>
                    <a href="#cases" class="text-white/80 hover:text-white transition-custom">客户案例</a>
                    <a href="#contact" class="text-white/80 hover:text-white transition-custom">联系我们</a>
                </nav>
                
                <div class="flex items-center">
                    <a href="https://haicont-test.coli688.com" target="_blank" class="px-4 py-2 bg-primary text-white rounded-lg shadow-md hover:bg-primary/90 transition-custom">登录</a>
                    
                    <!-- 移动端菜单按钮 -->
                    <button id="menu-toggle" class="md:hidden ml-4 text-white focus:outline-none">
                        <i class="fa-solid fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端导航菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-dark/95 bg-glass shadow-lg absolute w-full text-white">
            <div class="px-4 py-3 space-y-3">
                <a href="#features" class="block text-white/80 hover:text-white transition-custom">产品服务</a>
                <a href="#values" class="block text-white/80 hover:text-white transition-custom">服务价值</a>
                <a href="#advantages" class="block text-white/80 hover:text-white transition-custom">服务优势</a>
                <a href="#cases" class="block text-white/80 hover:text-white transition-custom">客户案例</a>
                <a href="#contact" class="block text-white/80 hover:text-white transition-custom">联系我们</a>
                <a href="https://haicont-test.coli688.com" target="_blank" class="block text-white/80 hover:text-white transition-custom">登录</a>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main>
        <!-- Banner 区域 -->
        <section class="pt-24 md:pt-32 pb-16 md:pb-24 bg-dark text-white relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 -z-10"></div>
            <div class="absolute top-0 right-0 w-full h-full">
                <img src="../images/website/banner.png" alt="背景" class="w-full h-full object-cover opacity-20">
            </div>
            <div class="container pt-12 mx-auto px-side md:px-side relative z-10"> <!-- 使用自定义内边距 -->
                <div class="flex flex-col md:flex-row items-center">
                    <div class="md:w-1/2 mb-10 md:mb-0">
                        <h1 class="text-[clamp(2.5rem,6vw,4rem)] font-bold leading-tight mb-4">
                            <span class="bg-gradient-to-r from-primary to-accent text-gradient">HAI 海小合</span>
                        </h1>
                        <p class="text-[clamp(1.2rem,3vw,1.8rem)] text-white/90 mb-8 max-w-xl">
                            基于AI大模型打造的地产合同服务智能体
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="https://haicont-test.coli688.com" target="_blank" class="px-8 py-3 bg-primary text-white rounded-lg shadow-lg hover:shadow-xl hover:bg-primary/90 transition-custom text-center font-medium">
                                立即登录
                            </a>
                            <a href="#contact" class="px-8 py-3 bg-transparent border border-white/30 text-white rounded-lg hover:bg-white/10 transition-custom text-center font-medium flex items-center justify-center">
                                <i class="fa-regular fa-play-circle mr-2"></i> 联系我们
                            </a>
                        </div>
                    </div>
                    <div class="md:w-1/2 relative">
                        <!-- 修正：移除图片倾斜，增强悬停效果 -->
                        <div class="relative z-10 rounded-xl overflow-hidden shadow-2xl transition-custom scale-hover w-[80%] mx-auto">
                            <img src="../images/website/banner.png" alt="地产AI合同服务平台界面" class="w-full h-auto">
                            <div class="absolute inset-0 bg-black/15 transition-opacity duration-300"></div>
                        </div>
                        <div class="absolute -bottom-10 -left-10 w-40 h-40 bg-primary/30 rounded-full filter blur-3xl -z-10"></div>
                        <div class="absolute -top-10 -right-10 w-50 h-50 bg-secondary/30 rounded-full filter blur-3xl -z-10"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 产品服务介绍 -->
        <section id="features" class="section-padding bg-white">
            <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
                <div class="text-center mb-16">
                    <h2 class="text-[clamp(2rem,5vw,3rem)] font-bold text-dark mb-4">产品服务</h2>
                    <p class="text-neutral-500 text-lg whitespace-nowrap">基于AI大模型，我们提供全方位的地产合同智能服务，助力企业高效管理合同全生命周期</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- 功能卡片 1 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon">
                            <i class="fa-solid fa-file-shield text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">地产合同风险审查</h3>
                        <p class="text-neutral-500 text-lg">智能审查地产项目合同，输出审查结果与分析建议，帮助企业提前识别潜在风险，保障合同合法合规。</p>
                    </div>
                    
                    <!-- 功能卡片 2 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon">
                            <i class="fa-solid fa-highlighter text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">地产合同字段抽取</h3>
                        <p class="text-neutral-500 text-lg">智能抽取地产合同字段要素，快速提取合同关键信息，减少人工录入错误，提高信息处理效率。</p>
                    </div>
                    
                    <!-- 功能卡片 3 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon">
                            <i class="fa-solid fa-code-compare text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">地产合同变更比对</h3>
                        <p class="text-neutral-500 text-lg">智能检测不同版本合同、标准条款与修订合同间的变更，并对变更风险进行智能评估，确保变更合规可控。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务价值介绍 (已合并AI赋能效果) -->
        <section id="values" class="section-padding bg-neutral-100">
            <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
                <div class="text-center mb-16">
                    <h2 class="text-[clamp(2rem,5vw,3rem)] font-bold text-dark mb-4">服务价值</h2>
                    <p class="text-neutral-500 max-w-2xl mx-auto text-lg">我们的AI合同服务为地产企业带来显著的业务价值提升，推动数字化转型</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- 价值卡片 1 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon bg-secondary/10">
                            <i class="fa-solid fa-rocket text-secondary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">全面提升合同管理效率</h3>
                        <p class="text-neutral-500 text-lg">将合同审查、信息抽取、变更比对由小时级提升至分钟级，大幅缩短业务处理周期，加速项目推进。</p>
                    </div>
                    
                    <!-- 价值卡片 2 -->
                    <div class="optimized-card">
                        <!-- 修正：添加缺失的图标 -->
                        <div class="optimized-card-icon bg-secondary/10">
                            <i class="fa-solid fa-check-circle text-secondary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">显著提升合同管理质量</h3>
                        <p class="text-neutral-500 text-lg">专业模型与业务知识赋能，让每一项输出都具备资深业务水准，降低合同风险，保障业务合规。</p>
                    </div>
                    
                    <!-- 价值卡片 3 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon bg-secondary/10">
                            <i class="fa-solid fa-database text-secondary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">助力企业数字化资产构建</h3>
                        <p class="text-neutral-500 text-lg">在业务配置与协作中沉淀行业专业与企业私有知识，快速转化为数字生产力，构建企业核心竞争力。</p>
                    </div>
                </div>
                
                <!-- AI赋能效果对比模块 -->
                <div class="ai-comparison">
                    <!-- 新增的背景图片结构 -->
                    <div class="ai-comparison-header">
                        <div class="ai-comparison-header-bg"></div>
                        <div class="ai-comparison-header-overlay"></div>
                        <div class="ai-comparison-header-content">
                            <h3 class="text-2xl font-semibold text-white mb-2">AI赋能效果</h3>
                            <p class="text-white/90 max-w-2xl mx-auto">我们的AI技术为地产合同管理带来全方位的提升，显著优化业务流程与效果</p>
                        </div>
                    </div>
                    <div class="ai-comparison-body">
                        <div class="ai-comparison-item before">
                            <h3>AI赋能前</h3>
                            <ul>
                                <li>纯人工识别，耗时费力</li>
                                <li>专业经验要求高，识别易遗漏</li>
                                <li>知识资产未沉淀，专业经验无法复用</li>
                            </ul>
                        </div>
                        <div class="ai-comparison-item after">
                            <h3>AI赋能后</h3>
                            <ul>
                                <li>AI智能识别，业务效率提升90%+</li>
                                <li>AI辅助结合人工复核，准确率提升至90%+</li>
                                <li>业务规则与案例沉淀，持续提升识别效果</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务优势介绍 -->
        <section id="advantages" class="section-padding bg-white">
            <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
                <div class="text-center mb-16">
                    <h2 class="text-[clamp(2rem,5vw,3rem)] font-bold text-dark mb-4">服务优势</h2>
                    <p class="text-neutral-500 mx-auto text-lg">我们的AI合同服务在地产行业具有显著优势，为企业提供专业、安全、高效的解决方案</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- 优势卡片 1 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon">
                            <i class="fa-solid fa-building text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">地产行业优势</h3>
                        <p class="text-neutral-500 text-lg">结合行业合同、行业专业项目知识、行业法规合约、法院公开实际纠纷案例等数据，训练地产合同场景专业模型，对比外部通用产品，在多种地产合同类型审查方面具有明显优势。</p>
                    </div>
                    
                    <!-- 优势卡片 2 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon">
                            <i class="fa-solid fa-lock text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">私有部署架构</h3>
                        <p class="text-neutral-500 text-lg">采用100%私有部署架构，从底层模型到系统工程均支持私有化部署，充分保障合同与项目信息安全，满足企业数据隐私要求。</p>
                    </div>
                    
                    <!-- 优势卡片 3 -->
                    <div class="optimized-card">
                        <div class="optimized-card-icon">
                            <i class="fa-solid fa-users text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-dark mb-4">企业级协作支持</h3>
                        <p class="text-neutral-500 text-lg">充分适应地产集团多层级、多区域、多职能、多业务系统的组织管理架构，支持不同形式的授权管理、接入方式与任务协作，提升团队协同效率。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 客户案例 -->
        <section id="cases" class="section-padding bg-white text-dark">
            <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
                <div class="text-center mb-16">
                    <h2 class="text-[clamp(2rem,5vw,3rem)] font-bold mb-4">客户案例</h2>
                    <p class="text-neutral-500 mx-auto text-lg">我们的AI合同服务已成功应用于多家知名地产企业，为其提供专业、高效的合同管理解决方案</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- 案例卡片 1 -->
                    <div class="bg-white rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-custom scale-hover relative">
                        <!-- 标签 -->
                        <div class="absolute z-10 bg-gradient-to-r from-[#499dff] to-[#507cff] rounded-br-xl md:rounded-br-2xl rounded-tl-xl md:rounded-tl-2xl p-1 md:p-3 text-[10px] md:text-xs text-white">
                            国资龙头房企
                        </div>
                        <div class="h-64 overflow-hidden relative">
                            <img src="../images/website/case/<EMAIL>" alt="中海地产案例" class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center mb-3">
                                <img src="../images/website/case/<EMAIL>" alt="中海地产 Logo" class="h-[30px] md:h-11 w-auto">
                            </div>
                            <p class="text-neutral-500 mb-6 ">1979年创立，1992年上市。连续20年获可持续蓝筹地产企业、中国房地产行业领导品牌。</p>
                        </div>
                    </div>
                    
                    <!-- 案例卡片 2 -->
                    <div class="bg-white rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-custom scale-hover relative">
                        <!-- 标签 -->
                        <div class="absolute z-10 bg-gradient-to-r from-[#499dff] to-[#507cff] rounded-br-xl md:rounded-br-2xl rounded-tl-xl md:rounded-tl-2xl p-1 md:p-3 text-[10px] md:text-xs text-white">
                            供应链数字化引领者
                        </div>
                        <div class="h-64 overflow-hidden relative">
                            <img src="../images/website/case/<EMAIL>" alt="领潮供应链案例" class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center mb-3">
                                <img src="../images/website/case/<EMAIL>" alt="中海地产 Logo" class="h-[30px] md:h-11 w-auto">
                            </div>
                            <p class="text-neutral-500 mb-6">中海领潮供应链公司，2020年创立。中海地产对外共享成本领先战略管理模式和集采标准的资源平台。</p>
                        </div>
                    </div>
                    
                    <!-- 案例卡片 3 -->
                    <div class="bg-white rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-custom scale-hover relative">
                        <!-- 标签 -->
                        <div class="absolute z-10 bg-gradient-to-r from-[#499dff] to-[#507cff] rounded-br-xl md:rounded-br-2xl rounded-tl-xl md:rounded-tl-2xl p-1 md:p-3 text-[10px] md:text-xs text-white">
                            地产数智化先锋
                        </div>
                        <div class="h-64 overflow-hidden relative">
                            <img src="../images/website/case/<EMAIL>" alt="中海科技案例" class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center mb-3">
                                <img src="../images/website/case/zhkj-logo.png" alt="中海地产 Logo" class="h-[30px] md:h-11 w-auto">
                            </div>
                            <p class="text-neutral-500 mb-6">中国海外发展有限公司旗下从事“不动产科技研发和服务的”科技公司，国家专精特新“小巨人”企业、国家高新技术企业、国资国企数字化转型试点企业。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系我们 -->
        <section id="contact" class="section-padding bg-white">
            <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
                <div class="text-center mb-16">
                    <h2 class="text-[clamp(2rem,5vw,3rem)] font-bold text-dark mb-4">联系我们</h2>
                    <p class="text-neutral-500 max-w-2xl mx-auto text-lg">如需了解更多关于我们的AI合同服务，或有任何疑问，欢迎随时联系我们</p>
                </div>
                
                <div class="max-w-5xl mx-auto">
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-neutral-200">
                        <div class="bg-gradient-to-br from-primary to-secondary p-8 md:p-12 text-white">
                            <h3 class="text-3xl font-semibold mb-6">联系方式</h3>
                            <div class="space-y-8">
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mr-4 flex-shrink-0">
                                        <i class="fa-solid fa-map-marker-alt text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-xl font-medium mb-2">公司地址</h4>
                                        <p class="opacity-90 text-lg">深圳市南山区汇通大厦8-10层</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mr-4 flex-shrink-0">
                                        <i class="fa-solid fa-phone text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-xl font-medium mb-2">联系电话</h4>
                                        <p class="opacity-90 text-lg">0755-8665 9066</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mr-4 flex-shrink-0">
                                        <i class="fa-solid fa-envelope text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-xl font-medium mb-2">电子邮箱</h4>
                                        <p class="opacity-90 text-lg"><EMAIL></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-white pt-16 pb-8">
        <div class="container mx-auto px-side md:px-side"> <!-- 使用自定义内边距 -->
            <div class="flex flex-col md:flex-row gap-32 mb-12">
                <div class="flex flex-col h-full">
                    <h3 class="text-2xl font-bold mb-6">HAI 海小合</h3>
                    <p class="text-white/70 mb-6 text-lg">基于AI大模型打造的地产合同服务智能体</p>
                    <div class="flex space-x-4 mt-auto">
                        <a href="#" class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-custom">
                            <i class="fa-brands fa-weixin"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-custom">
                            <i class="fa-brands fa-weibo"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-custom">
                            <i class="fa-brands fa-linkedin"></i>
                        </a>
                    </div>
                </div>
                
                <div class="flex flex-col h-full">
                    <h4 class="text-xl font-semibold mb-6">产品服务</h4>
                    <ul class="space-y-4 flex-1">
                        <li><a href="#features" class="text-white/70 hover:text-white transition-custom text-lg">合同风险审查</a></li>
                        <li><a href="#features" class="text-white/70 hover:text-white transition-custom text-lg">合同字段抽取</a></li>
                        <li><a href="#features" class="text-white/70 hover:text-white transition-custom text-lg">合同变更比对</a></li>
                    </ul>
                </div>
                
                <div class="flex flex-col h-full">
                    <h4 class="text-xl font-semibold mb-6">关于我们</h4>
                    <ul class="space-y-4 flex-1">
                        <li><a href="https://www.haizhichuang.cn" target="_blank" class="text-white/70 hover:text-white transition-custom text-lg">公司介绍</a></li>
                        <li><a href="#cases" class="text-white/70 hover:text-white transition-custom text-lg">客户案例</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-white/50 text-lg mb-4 md:mb-0">© 2025 HAI 海小合. 保留所有权利.</p>
                <div class="flex space-x-6">
                    <a href="#" class="text-white/50 hover:text-white transition-custom text-lg">服务条款</a>
                    <a href="#" class="text-white/50 hover:text-white transition-custom text-lg">隐私政策</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button id="back-to-top" class="fixed bottom-8 right-8 w-14 h-14 bg-primary text-white rounded-full shadow-lg flex items-center justify-center opacity-0 invisible transition-all duration-300 hover:bg-primary/90 z-50">
        <i class="fa-solid fa-arrow-up text-xl"></i>
    </button>

    <!-- JavaScript -->
    <script>
        // 导航栏滚动效果
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('shadow-lg');
            } else {
                navbar.classList.remove('shadow-lg');
            }
        });
        
        // 移动端菜单
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                // 关闭移动菜单（如果打开）
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // 回到顶部按钮
        const backToTopButton = document.getElementById('back-to-top');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.add('opacity-0', 'invisible');
                backToTopButton.classList.remove('opacity-100', 'visible');
            }
        });
        
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>    