# Markdown 组件演示

这是一个功能完整的 Markdown 渲染组件示例，支持 GitHub 风格样式和代码高亮。

## 功能特性

- ✅ **GitHub 风格样式**: 使用 `github-markdown-css` 提供美观的样式
- ✅ **代码语法高亮**: 支持多种编程语言的语法高亮
- ✅ **数学公式渲染**: 支持 LaTeX 数学公式
- ✅ **文件加载**: 可以从文件路径加载 Markdown 内容
- ✅ **实时内容更新**: 支持动态更新内容

## 代码高亮示例

### JavaScript 代码
```javascript
// Vue 3 组合式 API 示例
import { ref, computed } from 'vue';

export default {
  setup() {
    const count = ref(0);
    const doubled = computed(() => count.value * 2);
    
    const increment = () => {
      count.value++;
    };
    
    return {
      count,
      doubled,
      increment
    };
  }
};
```

### Python 代码
```python
# Python 数据处理示例
import pandas as pd
import numpy as np

def process_data(data):
    """处理数据并返回结果"""
    df = pd.DataFrame(data)
    result = df.groupby('category').agg({
        'value': ['mean', 'sum', 'count']
    })
    return result

# 使用示例
sample_data = [
    {'category': 'A', 'value': 10},
    {'category': 'B', 'value': 20},
    {'category': 'A', 'value': 15}
]

result = process_data(sample_data)
print(result)
```

### TypeScript 代码
```typescript
// TypeScript 接口定义
interface MarkdownProps {
  content?: string;
  filePath?: string;
  loading?: boolean;
  className?: string;
}

class MarkdownRenderer {
  private props: MarkdownProps;
  
  constructor(props: MarkdownProps) {
    this.props = props;
  }
  
  public render(): string {
    if (!this.props.content) {
      return '<div>No content</div>';
    }
    return this.parseMarkdown(this.props.content);
  }
  
  private parseMarkdown(content: string): string {
    // 解析逻辑
    return content;
  }
}
```

## 数学公式

支持行内公式和块级公式：

行内公式：质能方程 $E = mc^2$

块级公式：
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

更复杂的公式：
$$
\frac{\partial}{\partial t}\Psi(x,t) = \frac{i\hbar}{2m}\frac{\partial^2}{\partial x^2}\Psi(x,t) + V(x)\Psi(x,t)
$$

## 表格

| 功能 | 支持状态 | 说明 |
|------|----------|------|
| 基础 Markdown | ✅ | 标题、段落、列表等 |
| 代码高亮 | ✅ | 多语言语法高亮 |
| 数学公式 | ✅ | LaTeX 格式 |
| 表格 | ✅ | 支持对齐 |
| 图片 | ✅ | 支持本地和网络图片 |
| 链接 | ✅ | 自动识别和手动链接 |

## 列表

### 无序列表
- 第一项
- 第二项
  - 嵌套项 1
  - 嵌套项 2
- 第三项

### 有序列表
1. 步骤一：安装依赖
2. 步骤二：配置组件
3. 步骤三：使用组件
   1. 导入组件
   2. 设置属性
   3. 渲染内容

## 引用

> 这是一个引用块的示例。
> 
> 引用可以包含多个段落，并且可以嵌套。
> 
> > 这是嵌套引用。

## 强调

**粗体文本** 和 *斜体文本* 以及 ~~删除线文本~~

## 链接

- [Vue.js 官网](https://vuejs.org/)
- [TDesign 组件库](https://tdesign.tencent.com/)
- [Markdown-it 解析器](https://github.com/markdown-it/markdown-it)

## 水平分割线

---

## 总结

这个 Markdown 组件提供了完整的渲染功能，包括：
- 美观的 GitHub 风格样式
- 完整的语法高亮支持
- 数学公式渲染
- 响应式设计
- 加载状态处理

非常适合用于文档展示、博客系统或任何需要 Markdown 渲染的场景。 