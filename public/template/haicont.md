# 海小合

Base URLs:

* <a href="https://haicont-test.coli688.com/coli-ai-contract-app">测试环境: https://haicont-test.coli688.com/coli-ai-contract-app</a>

# Authentication

# 审查规则/规则清单

## POST 查询可用的规则清单列表

POST /reviewRuleList/listUsefulReviewRuleList

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": true,
  "errCode": null,
  "errMessage": null,
  "resultCode": 0,
  "data": [
    {
      "id": "string",
      "tenantId": "string",
      "userId": "string",
      "name": "string",
      "description": "string",
      "share": 0,
      "shareScope": "string",
      "sourceType": "string",
      "createdName": "string",
      "createdTime": "string",
      "updatedName": "string",
      "updatedTime": "string",
      "enable": 0,
      "ruleVos": [
        {
          "ruleListId": "string",
          "id": "string",
          "tenantId": "string",
          "userId": "string",
          "code": "string",
          "name": "string",
          "rule": "string",
          "ruleSource": "string",
          "riskLevel": "string",
          "retrievalText": "string",
          "prompts": "string",
          "share": 0,
          "exampleList": [
            "string"
          ]
        }
      ],
      "shareTenants": null
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» success|boolean|true|none||none|
|» errCode|null|true|none||none|
|» errMessage|null|true|none||none|
|» resultCode|integer|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|true|none|清单ID|none|
|»» tenantId|string|true|none|租户ID|none|
|»» userId|string|true|none|用户ID|none|
|»» name|string|true|none|清单名称|none|
|»» description|string|true|none|清单描述|none|
|»» share|integer|true|none|是否开放|none|
|»» shareScope|string|true|none|开放范围|none|
|»» sourceType|string|true|none|来源|none|
|»» createdName|string|true|none||none|
|»» createdTime|string|true|none||none|
|»» updatedName|string|true|none||none|
|»» updatedTime|string|true|none||none|
|»» enable|integer|true|none|是否可用|none|
|»» ruleVos|[object]|true|none|审查项列表|none|
|»»» ruleListId|string|true|none|清单ID|none|
|»»» id|string|true|none|审查项ID|none|
|»»» tenantId|string|true|none|租住ID|none|
|»»» userId|string|true|none|用户ID|none|
|»»» code|string|true|none|审查项编码|none|
|»»» name|string|true|none|审查项名称|none|
|»»» rule|string|true|none|审查规则|none|
|»»» ruleSource|string|true|none|规则来源|none|
|»»» riskLevel|string|true|none|风险程度|none|
|»»» retrievalText|string¦null|true|none|召回文本|none|
|»»» prompts|string|true|none|大模型提示词|none|
|»»» share|integer|true|none|是否开放|none|
|»»» exampleList|[string]|true|none|示例列表|none|
|»» shareTenants|null|true|none||none|

# 合同审查任务

## POST 上传文件

POST /taskFiles/upload

> Body 请求参数

```yaml
file: file://C:\Users\<USER>\Downloads\陈塘 F7 地块项目标识标牌集中采购及施工工程（脱敏）.md

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 新建任务

POST /task/save

> Body 请求参数

```json
{
  "fileId": "1929863007022579713",
  "fileName": "测试xxxxxxxxxxxxxxxxxxx合同.docx",
  "includedRules": "100,101,102,103,104,105,106,107,108,109,110,111,112,113",
  "type": "REVIEW",
  "reviewRuleListName": "测试",
  "reviewRuleListId": "1",
  "reviewMode": "HIGH_SPEED",
  "reviewPageRange": "ALL",
  "modelId": 4
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» fileId|body|string| 是 | 文件ID|none|
|» fileName|body|string| 是 | 文件名称|none|
|» includedRules|body|string| 是 | 审查项ID|none|
|» type|body|string| 否 | 任务类型|EXTRACT, REVIEW|
|» reviewRuleListName|body|string| 是 | 审查清单名称|none|
|» reviewRuleListId|body|number| 是 | 审查清单ID|none|
|» reviewMode|body|string| 是 | 审查模式|HIGH_QUALITY-高精度、HIGH_SPEED-高性能|
|» reviewPageRange|body|string| 是 | 需要审查的页码范围|none|

> 返回示例

> 200 Response

```json
{
  "success": true,
  "errCode": "string",
  "errMessage": "string",
  "resultCode": 0,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» success|boolean|true|none||none|
|» errCode|string|true|none||none|
|» errMessage|string|true|none||none|
|» resultCode|integer|true|none||none|
|» data|null|true|none||none|

## GET 查询指定任务

GET /task/getById/2

> Body 请求参数

```yaml
{}

```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "success": true,
  "errCode": null,
  "errMessage": null,
  "resultCode": 0,
  "data": {
    "id": "2",
    "tenantId": "100",
    "userId": "20210607155245372-481C-23A3302FA",
    "code": "AIC_100_2",
    "fileId": "1932992216811929601",
    "fileName": "合同文件（例）.pdf",
    "llmFileId": "",
    "status": "COMPLETED",
    "includedRules": "1925481171077066755,1925481171077066754,1925481171072872449",
    "ruleCount": 3,
    "excludedRules": null,
    "type": "REVIEW",
    "reviewRuleListName": "承包类合同",
    "reviewRuleListId": "165",
    "reviewMode": "HIGH_SPEED",
    "reviewPageRange": "ALL",
    "modelId": "2",
    "modelName": "QWEN",
    "datasetId": null,
    "errorCode": "",
    "errorMessage": "",
    "totalDuration": 65,
    "fileDuration": 17,
    "llmDuration": 19,
    "createdBy": "20210607155245372-481C-23A3302FA",
    "createdName": "miaozhendong",
    "createdTime": "2025-06-12 10:43:54",
    "updatedBy": "anonymous",
    "updatedName": "anonymous",
    "updatedTime": "2025-06-13 11:11:41",
    "deleted": 0,
    "resultsVoList": [
      {
        "id": "1933360338928709634",
        "tenantId": "100",
        "userId": "20210607155245372-481C-23A3302FA",
        "taskId": "2",
        "reviewRuleName": "付款时间规范检查",
        "reviewRuleId": "1925481171077066755",
        "modelName": "QWEN",
        "result": "PASSED",
        "riskLevel": "HIGH",
        "status": "COMPLETED",
        "sourceText": "[{\"highlight_text\":\"10.3.6 发包人在完成付款申请审批后的二十八日历天内，支付承包人当期应得工程款项\",\"row_number\":\"500\"},{\"highlight_text\":\"除非另有说明，发包人以转账方式支付承包人，而不论承包人何时何地于任何银 行账户入账\",\"row_number\":\"501\"}]",
        "comments": "根据审查片段中的内容，10.3.6条已经修改为“发包人在完成付款申请审批后的二十八日历天内，支付承包人当期应得工程款项”，符合审查要点的要求。审查片段中的10.3.6条已经明确修改为“发包人在完成付款申请审批后的二十八日历天内，支付承包人当期应得工程款项”，与审查要点中的要求完全一致。因此，审查结论为通过。",
        "createdBy": "anonymous",
        "createdName": "anonymous",
        "createdTime": "2025-06-13 11:06:40",
        "updatedBy": "anonymous",
        "updatedName": "anonymous",
        "updatedTime": "2025-06-13 11:06:40",
        "deleted": 0
      },
      {
        "id": "1933360338903543810",
        "tenantId": "100",
        "userId": "20210607155245372-481C-23A3302FA",
        "taskId": "2",
        "reviewRuleName": "关键工期节点数量检查",
        "reviewRuleId": "1925481171077066754",
        "modelName": "QWEN",
        "result": "PASSED",
        "riskLevel": "MEDIUM",
        "status": "COMPLETED",
        "sourceText": "[{\"highlight_text\":\"4.2 承包人须根据发包人进度要求进行施工，具体关键节点工期约定如下：\",\"row_number\":\"127\"},{\"highlight_text\":\"合同内所有标识完成\",\"row_number\":\"134\"},{\"highlight_text\":\"竣工备案完成\",\"row_number\":\"138\"},{\"highlight_text\":\"集中入伙完成\",\"row_number\":\"142\"}]",
        "comments": "根据合同协议书中的内容，关键工期节点明确列出了3个，分别是合同内所有标识完成、竣工备案完成、集中入伙完成。这些节点的数量没有超过5个，符合审查要求。",
        "createdBy": "anonymous",
        "createdName": "anonymous",
        "createdTime": "2025-06-13 11:06:40",
        "updatedBy": "anonymous",
        "updatedName": "anonymous",
        "updatedTime": "2025-06-13 11:06:40",
        "deleted": 0
      },
      {
        "id": "1933360338823852033",
        "tenantId": "100",
        "userId": "20210607155245372-481C-23A3302FA",
        "taskId": "2",
        "reviewRuleName": "签署页完整性检查",
        "reviewRuleId": "1925481171072872449",
        "modelName": "QWEN",
        "result": "PASSED",
        "riskLevel": "HIGH",
        "status": "COMPLETED",
        "sourceText": "[{\"highlight_text\":\"地址： 天津市东丽区大毕庄金钟科\",\"row_number\":\"285\"},{\"highlight_text\":\"道南侧郡都大厦 102-15\",\"row_number\":\"286\"},{\"highlight_text\":\"统一社会信用代码：\",\"row_number\":\"289\"},{\"highlight_text\":\"91120103MA82BG5419\",\"row_number\":\"290\"},{\"highlight_text\":\"邮编： 300240\",\"row_number\":\"292\"},{\"highlight_text\":\"联系人： 蒋兆南\",\"row_number\":\"293\"},{\"highlight_text\":\"电话： 18522842666\",\"row_number\":\"294\"},{\"highlight_text\":\"Email： <EMAIL>\",\"row_number\":\"295\"}]",
        "comments": "根据提供的审查片段，合同协议书签署页内容完整，承包人的地址、社会信用代码、邮编、联系人、电话、邮箱均已填写。因此，所有审查要点均通过。",
        "createdBy": "anonymous",
        "createdName": "anonymous",
        "createdTime": "2025-06-13 11:06:40",
        "updatedBy": "anonymous",
        "updatedName": "anonymous",
        "updatedTime": "2025-06-13 11:06:40",
        "deleted": 0
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» success|boolean|true|none|是否成功|none|
|» errCode|null|true|none|错误码|none|
|» errMessage|null|true|none|异常信息|none|
|» resultCode|integer|true|none|响应码|0表示成功|
|» data|object|true|none|任务详情|none|
|»» id|string|true|none|任务ID|none|
|»» tenantId|string|true|none|租户ID|none|
|»» userId|string|true|none|用户ID|none|
|»» code|string|true|none|任务编码|none|
|»» fileId|string|true|none|文件ID|none|
|»» fileName|string|true|none|文件名称|none|
|»» llmFileId|string|true|none|大模型文件ID|none|
|»» status|string|true|none|任务状态|* 文档分析中、审查排队中、审查中、已完成、任务失败、任务无效、任务终止<br />     * ANALYSIS、QUEUED、REVIEWING、COMPLETED、FAILED、INVALID、TERMINATED|
|»» includedRules|string|true|none|带审查规则ID|none|
|»» ruleCount|integer|true|none|规则总数|none|
|»» excludedRules|null|true|none|排除规则ID|none|
|»» type|string|true|none|任务类型|EXTRACT, REVIEW|
|»» reviewRuleListName|string|true|none|审查清单名称|none|
|»» reviewRuleListId|string|true|none|审查清单ID|none|
|»» reviewMode|string|true|none|审查模式|HIGH_QUALITY-高精度、HIGH_SPEED-高性能|
|»» reviewPageRange|string|true|none|需要审查的页码范围|ALL 全部、1,200  页码范围1-200|
|»» modelId|string|true|none|适配的模型ID|none|
|»» modelName|string|true|none|适配的模型名称|none|
|»» datasetId|null|true|none|临时知识库ID|none|
|»» errorCode|string|true|none|异常编码|none|
|»» errorMessage|string|true|none|异常信息|none|
|»» totalDuration|integer|true|none|总耗时|none|
|»» fileDuration|integer|true|none|文件解析耗时|none|
|»» llmDuration|integer|true|none|文件审查耗时|none|
|»» createdBy|string|true|none||none|
|»» createdName|string|true|none||none|
|»» createdTime|string|true|none||none|
|»» updatedBy|string|true|none||none|
|»» updatedName|string|true|none||none|
|»» updatedTime|string|true|none||none|
|»» deleted|integer|true|none||none|
|»» resultsVoList|[object]|true|none|审查结果|none|
|»»» id|string|true|none|审查结果ID|none|
|»»» tenantId|string|true|none|租户ID|none|
|»»» userId|string|true|none|用户ID|none|
|»»» taskId|string|true|none|审查任务I D|none|
|»»» reviewRuleName|string|true|none|审查项名称|none|
|»»» reviewRuleId|string|true|none|审查项你D|none|
|»»» modelName|string|true|none|大模型名称|none|
|»»» result|string|true|none|审查结果|PASSED-通过，FAILED-有风险|
|»»» riskLevel|string|true|none|风险等级|NONE-无风险、LOW-低风险、MEDIUM-中风险、HIGH-高风险|
|»»» status|string|true|none|审查状态|REVIEWING-审查中，COMPLETED-已完成，FAILED-失败|
|»»» sourceText|string|true|none|相关原文|可以是多个，行号、片段、句子|
|»»» comments|string|true|none|审查备注或说明|none|
|»»» createdBy|string|true|none||none|
|»»» createdName|string|true|none||none|
|»»» createdTime|string|true|none||none|
|»»» updatedBy|string|true|none||none|
|»»» updatedName|string|true|none||none|
|»»» updatedTime|string|true|none||none|
|»»» deleted|integer|true|none||none|

# 数据模型

