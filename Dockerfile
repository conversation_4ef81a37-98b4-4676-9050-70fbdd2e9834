FROM ***********:30005/library/nginx:latest
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone
RUN rm -rf /etc/nginx/conf.d/gzip.conf
RUN rm -rf /etc/nginx/conf.d/default.conf
ADD default.conf /etc/nginx/conf.d/default.conf
RUN touch  /etc/nginx/conf.d/gzip.conf
RUN echo 'gzip  on;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_min_length 1k;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_buffers 4 16k;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_http_version 1.0;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_comp_level 2;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_vary off;' >>/etc/nginx/conf.d/gzip.conf
RUN echo 'gzip_disable "MSIE [1-6]\.";' >>/etc/nginx/conf.d/gzip.conf

RUN rm -rf /usr/share/nginx/html
RUN mkdir -p /usr/share/nginx/html

COPY dist/ /usr/share/nginx/html/
EXPOSE 80
ENTRYPOINT nginx -g "daemon off;"
