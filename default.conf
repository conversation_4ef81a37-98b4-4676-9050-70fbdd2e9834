upstream csces-news-craft-app {
    server csces-news-craft-app-http:80;
    keepalive 300;
}

server {
    listen       80;
    server_name  localhost;
    client_max_body_size 100M;
    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;

        # 设置HTML文件不缓存
        #if ($request_filename ~* ^.*?.(html|htm)$) {
        #    add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
        #}
    }

    # location ~ /iam/ {
    #     proxy_pass https://dev-aicopilot-apis.coli688.com;  # 后端服务地址
    # }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
