interface ImportMetaEnv {
  readonly VITE_BASE_URL: string;
  readonly VITE_IS_REQUEST_PROXY: string;
  readonly VITE_API_URL: string;
  readonly VITE_API_URL_PREFIX: string;
  readonly VITE_SYS_API_URL_PREFIX: string;
  readonly VITE_TOKEN_NAME: string;
  readonly VITE_AES_KEY: string;
  readonly VITE_SSO_ENABLE: string;
  readonly VITE_APP_CODE: string;
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
