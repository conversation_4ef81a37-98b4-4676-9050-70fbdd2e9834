/**
 * Tabs 页签组件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/12/27 11:07:47
 * LastEditors: mi<PERSON><PERSON>@live.com
 * LastEditTime: 2022/12/27 11:07:47
 */
import { defineComponent, PropType } from 'vue'
import style from './index.module.less'

export default defineComponent({
  name: 'TabsIndex',
  props: {
    /**
     * 当前选中Tab索引
     */
    modelValue: {
      type: Number,
      default: null
    },
    /**
     * Tabs数据列表
     */
    data: {
      type: Array as PropType<string[]>,
      request: true,
      default: () => [] 
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    return {
      /**
       * Tab切换事件
       * @param index
       * @returns {any}
       */
      tabsSwitch: (index: number) => {
        emit('update:modelValue', index)
      }
    }
  },
  render() {
    return <div class={style['tabs-container']}>
      {
        this.$props.data.map((item: string, index: number) => {
          return <div
            class={['tab', this.$props.modelValue === index ? 'active' : '']}
            onClick={() => this.tabsSwitch(index)}>
            <div class="label">{item}</div>
          </div>
        })
      }
    </div>
  }
})
