/*
 * Tabs 页签组件样式
 * @Author: <PERSON><PERSON>@live.com
 * @Date: 2022/12/27 13:42:39
 * @LastEditors: mi<PERSON><PERSON>@live.com
 * @LastEditTime: 2022/12/27 13:42:39
 */

.tabs-container {
  display: flex;
  align-items: center;
  width: 100%;
  overflow-x: auto;
  border-bottom: 1px solid #dfe4e8;

  :global {
    .tab {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 104px;
      height: 60px;
      font-size: 14px;
      color: #454545;
      .label {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        cursor: pointer;
      }
    }

    .tab.active {
      font-weight: bolder;
      color: var(--td-brand-color-5);
      background-color: #fff;
      border-bottom: 2px solid var(--td-brand-color-5);
    }
  }
}
