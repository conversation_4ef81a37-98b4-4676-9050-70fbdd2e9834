# Tabs标签页组件
- 选项卡切换组件

### Props参数说明
- modelValue：当前选中项下标
- data：Tabs数据列表 <string[]>


## Example
```typescript jsx
import { defineComponent, ref } from 'vue'
import Tabs from '@/components/tabs'

export default defineComponent({
  name: 'DemoIndex',
  setup() {
    return {
      tabIndex: ref(0),
      tabs: ['组织选择', '我的常用']
    }
  },
  render() {
    return <Tabs v-model={this.tabIndex} data={this.tabs} />
  }
})
```

## 页面效果
![img.png](img.png)
