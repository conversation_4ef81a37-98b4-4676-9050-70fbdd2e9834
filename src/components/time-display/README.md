# TimeDisplay 时间显示组件

一个用于显示实时时间差的 TSX 组件，支持页签休眠处理。

## 功能特性

- ✅ **实时更新**：每秒自动更新时间显示
- ✅ **页签休眠处理**：页签隐藏时停止计时器，激活时自动恢复
- ✅ **智能计算**：基于创建时间实时计算到当前时间的差值
- ✅ **资源管理**：组件卸载时自动清理定时器和事件监听器
- ✅ **TypeScript 支持**：完整的类型定义
- ✅ **自定义样式**：支持 className 和 style 属性

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| createdTime | string | ✅ | - | 创建时间，格式：'YYYY-MM-DD HH:mm:ss' |
| className | string | ❌ | '' | 自定义CSS类名 |
| style | Record<string, string \| number> | ❌ | {} | 自定义样式对象 |

## 使用示例

### 基础用法

```tsx
import TimeDisplay from '@/components/time-display';

// JSX/TSX 中使用
<TimeDisplay createdTime="2024-01-01 10:30:00" />
```

### Vue Template 中使用

```vue
<template>
  <!-- 基础使用 -->
  <TimeDisplay :created-time="taskCreatedTime" />
  
  <!-- 带自定义样式 -->
  <TimeDisplay 
    :created-time="currentTask.createdTime"
    class-name="custom-time"
    :style="{ color: '#999', fontSize: '12px' }"
  />
  
  <!-- 在文本中使用 -->
  <div>
    运行时间: <TimeDisplay :created-time="currentTask.createdTime" />
  </div>
</template>

<script setup lang="ts">
import TimeDisplay from '@/components/time-display';

const taskCreatedTime = '2024-01-01 10:30:00';
</script>
```

### 结合业务场景

```vue
<template>
  <div class="task-status">
    <t-button :theme="getStatusTheme(task.status)">
      {{ getStatusText(task.status) }} {{ task.progress }}
    </t-button>
    <div v-if="isTaskRunning(task.status)" class="running-time">
      运行时间: <TimeDisplay :created-time="task.createdTime" />
    </div>
  </div>
</template>
```

## 显示格式

组件会自动格式化时间差显示：

- `5秒` - 小于1分钟
- `2分钟30秒` - 小于1小时
- `1小时30分钟20秒` - 超过1小时

## 技术实现

- 使用 `Page Visibility API` 处理页签休眠
- 基于 Vue 3 Composition API 和 TSX 语法
- 响应式时间计算，确保准确性
- 内存友好的定时器管理

## 注意事项

1. **时间格式**：确保 `createdTime` 参数格式正确
2. **性能优化**：组件会在页签隐藏时自动停止更新
3. **时区处理**：基于本地时间计算，确保时区一致性 