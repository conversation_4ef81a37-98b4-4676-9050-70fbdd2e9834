import { computed, defineComponent, onMounted, onUnmounted, ref } from 'vue';

import { getTimeDiff } from '@/utils/date';

export interface TimeDisplayProps {
  /**
   * 创建时间字符串，格式为 'YYYY-MM-DD HH:mm:ss'
   */
  createdTime: string;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 自定义样式
   */
  style?: Record<string, string | number>;
}

/**
 * 时间显示组件
 * 功能：
 * - 实时显示从创建时间到当前时间的时间差
 * - 自动处理页签休眠/激活状态
 * - 每秒更新显示内容
 * - 组件卸载时自动清理资源
 */
export default defineComponent({
  name: 'TimeDisplay',
  props: {
    createdTime: {
      type: String,
      required: true,
      default: '',
    },
    className: {
      type: String,
      default: '',
    },
    style: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props: TimeDisplayProps) {
    const currentTime = ref(new Date());
    const timer = ref<NodeJS.Timeout | null>(null);

    // 计算显示时间
    const displayTime = computed(() => {
      // 触发currentTime的响应式更新
      return getTimeDiff(props.createdTime) + (currentTime.value ? '' : '');
    });

    // 启动定时器
    const startTimer = () => {
      if (timer.value) return;
      timer.value = setInterval(() => {
        currentTime.value = new Date();
      }, 1000);
    };

    // 停止定时器
    const stopTimer = () => {
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
    };

    // 处理页面可见性变化
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopTimer();
      } else {
        // 页面重新可见时立即更新时间
        currentTime.value = new Date();
        startTimer();
      }
    };

    onMounted(() => {
      startTimer();
      document.addEventListener('visibilitychange', handleVisibilityChange);
    });

    onUnmounted(() => {
      stopTimer();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    });

    return () => (
      <span class={props.className} style={props.style}>
        {displayTime.value}
      </span>
    );
  },
});
