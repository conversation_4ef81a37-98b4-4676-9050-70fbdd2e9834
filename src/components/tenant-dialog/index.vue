<template>
  <t-dialog
    header="添加租户"
    :close-on-overlay-click="false"
    :visible="visible"
    top="5vh"
    width="800px"
    :confirm-btn="{ content: '确定', theme: 'primary' }"
    :cancel-btn="{ content: '取消' }"
    @confirm="onDialogConfirm"
    @close="onClose"
  >
    <t-form colon label-width="80px" label-align="right">
      <t-form-item label="已添加">
        <t-space break-line size="small">
          <t-tag
            v-for="(item, index) in tenantList"
            :key="item.listId"
            variant="outline"
            theme="primary"
            closable
            @close="handleTagClose(index)"
          >
            {{ item.tenantName || '-' }}
          </t-tag>
        </t-space>
      </t-form-item>
      <t-form-item label="查找">
        <div>
          <div class="search-container">
            <t-input
              v-model="keywords"
              placeholder="通过手机号或企业名称搜索"
              autofocus
              clearable
              @clear="onPageChange({ current: 1 })"
            >
              <template #suffixIcon>
                <search-icon />
              </template>
            </t-input>
            <t-button theme="primary" variant="outline" @click="onPageChange({ current: 1 })">搜索</t-button>
          </div>
          <t-table
            :data="tableData"
            :columns="columns"
            :pagination="pagination"
            row-key="tenantId"
            :loading="loading"
            @page-change="onPageChange"
          >
            <template #op="{ row }">
              <t-link v-if="!tenantIds.includes(row.tenantId)" theme="primary" hover="color" @click="onTagAdd(row)">
                添加
              </t-link>
              <t-link v-else hover="color">已添加</t-link>
            </template>
          </t-table>
        </div>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
<script lang="ts" setup>
import { SearchIcon } from 'tdesign-icons-vue-next';
import type { PrimaryTableCol } from 'tdesign-vue-next';
import { computed, reactive, ref, watch } from 'vue';

import { listShareTenants } from '@/api/modules/rule';

// 获取参数
const props = defineProps<{
  values: Array<any>;
  visible: boolean;
  ruleId?: string;
}>();
// 事件
const emits = defineEmits<{
  (e: 'update:values', data: Array<any>): void;
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: Array<any>): void;
}>();
const loading = ref(false);
const tenantList = ref([]); // 当前选中的租户列表
const tableData = ref([]); // 租户表格数据源
const keywords = ref(''); // 搜索关键词，用于租户查找
const columns: PrimaryTableCol[] = [
  { colKey: 'tenantName', title: '企业名称', minWidth: 150, ellipsis: true, fixed: 'left' },
  { colKey: 'tenantContactPhone', title: '手机号', minWidth: 150 },
  { colKey: 'tenantCreateTime', title: '账号创建时间', minWidth: 150 },
  { colKey: 'op', title: '操作', width: 100, fixed: 'right' },
];
const pagination = reactive<any>({
  total: 0,
  size: 10,
  current: 1,
}); // 分页信息
// 分页切换
const onPageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current;
  if (pageInfo.pageSize) pagination.size = pageInfo.pageSize;
  onSearch();
};
// 计算属性：获取当前选中租户的ID集合
const tenantIds = computed(() => {
  return tenantList.value.map((item: any) => item.tenantId);
});

// 关闭对话框时清空租户列表并隐藏对话框
const onClose = () => {
  tenantList.value = [];
  emits('update:visible', false); // 关闭对话框
};

// 确定选择租户后更新表单数据并关闭对话框
const onDialogConfirm = () => {
  emits('confirm', tenantList.value);
  onClose();
};
// 删除已选租户标签
const handleTagClose = (index: number) => {
  tenantList.value.splice(index, 1);
};
// 添加租户到选中列表
const onTagAdd = (row: any) => {
  tenantList.value.push(row);
};

// 租户搜索处理
const onSearch = async () => {
  loading.value = true;
  try {
    const params = {
      tenantName: keywords.value,
      listId: props.ruleId,
      size: pagination.size,
      current: pagination.current,
    };
    const res = await listShareTenants(params);
    tableData.value = res.records || [];
    pagination.total = Number(res.total) || 0;

    loading.value = false;
  } catch (error) {
    console.error('获取列表失败:', error);
    loading.value = false;
  }
};

// 初始化租户列表
watch(
  () => props.values,
  (val) => {
    tenantList.value = val;
  },
  { immediate: true },
);
// 监听对话框显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      onPageChange({ current: 1 });
    }
  },
  { immediate: true },
);
</script>
<style scoped lang="less">
.search-container {
  display: flex;
  margin-bottom: 12px;
  .t-button {
    margin-left: 10px;
  }
}
</style>
