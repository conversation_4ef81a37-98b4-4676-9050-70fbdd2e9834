/*
 * 搜索栏公共组件样式
 * @Author: <PERSON><PERSON>@live.com
 * @Date: 2022/10/17 12:03:29
 */
 .index-container {
  padding: 24px 24px 24px;
  background-color: #fff;
  border-radius: 8px;
  :global {
    // 表单样式
    .t-form {
      width: 100%;

      // 自定义行
      .row {
        display: flex;
        // align-items: center;
        .col {
          display: flex;
          // align-items: center;
        }
        .basic-col {
          flex: 1;
          .ant-form-item-control {
            height: 100%;
          }
        }
      }

      // 表单项
      .t-form__item {
        width: 208px;
        margin-bottom: 16px;
        .t-form__label {
          label {
            height: 36px;
            font-family: PingFangSC, PingFangSC-Semibold, sans-serif;
            font-size: 12px;
            font-weight: 600;
            color: #a3afbb;
            text-align: left;
          }
        }
      }
      .t-form__item + .t-form__item {
        margin-left: 8px;
      }
      // 右侧操作栏
      .operations-bar {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        min-height: 35px;
        padding-left: 32px;
        .operations {
          display: flex;
          align-items: center;
          cursor: pointer;
          .label {
            padding-right: 8px;
            color: v;
          }
        }
      }
    }
  }
}
