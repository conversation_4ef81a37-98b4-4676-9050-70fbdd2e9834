/**
 * 搜索栏公共组件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/10/17 11:46:51
 * LastEditors: miaozhendong <EMAIL>
 * LastEditTime: 2024-05-10
 */
import { defineComponent, ref, PropType } from 'vue'
import css from './index.module.less'
import { FormProps } from 'tdesign-vue-next'

export default defineComponent({
  name: 'SearchBar',
  props: {
    // 表单参数配置
    options: {
      type: Object as PropType<FormProps>,
      default: () => ({})
    }
  },
  // 注册回调事件
  emits: ['submit', 'reset'],
  setup(props, context) {
    const slots: Record<string, any> = context.slots
    const title = ref('高级筛选')
    const expand = ref(false)

    /**
     * 高级筛选切换
     */
    function showMoreHandle() {
      expand.value = !expand.value
    }

    /**
     * 搜索
     */
    function onSubmit() {
      context.emit('submit')
    }

    /**
     * 重置
     */
    function onReset() {
      context.emit('reset')
    }

    return () => <div class={css['index-container']}>
      <t-form {...props.options}>
        {
          // 筛选内容插槽
          slots.default && <div class="row">
            <div class="col basic-col">
              {slots.default()}
              <t-form-item class="buttons-bar">
                {
                  // 操作按钮内容插槽
                  slots.buttons && slots.buttons() || <>
                    <t-button class="button" onClick={onSubmit}>搜索</t-button>
                    <t-button class="button" onClick={onReset}>重置</t-button>
                  </>
                }
              </t-form-item>
            </div>
            {
              // 高级筛选
              slots.expands && <div class="col">
                <div class="operations-bar">
                  <div class="operations" onClick={showMoreHandle}>
                    <span class="label">{title.value}</span>
                    <t-icon name={expand.value ? 'chevron-up' : 'chevron-down'} />
                  </div>
                </div>
              </div>
            }
            {slots?.btnRight?.()}
          </div>
        }

        {
          // 高级筛选内容插槽
          expand.value && <div class="row expand-row">{slots.expands?.()}</div>
        }
      </t-form>
    </div>
  }
})
