# 筛选栏组件
- 基于Ant Form组件封装
- 全局注册，页面模板可直接使用
- 组件参数支持动态注入

## 代码演示
```jsx
<SearchBar options={searchOptions} class="search-bar" v-slots={{
  // 筛选内容插槽
  default: () => <>
    <a-form-item name="deptName">
      <a-input v-model={formState.deptName} autofocus allowClear placeholder="部门名称"/>
    </a-form-item>
    <a-form-item name="status" placeholder="部门状态">
      <a-select v-model={formState.status} options={selectOptions} allowClear placeholder="部门状态"/>
    </a-form-item>
  </>,
  // 操作按钮
  buttons: () => <>
    <a-button class="buttons submit">搜索</a-button>
    <a-button class="buttons reset">重置</a-button>
  </>,
  // 高级筛选内容插槽
  expands: () => <>
    <a-form-item name="deptName" label="部门名称" placeholder="部门名称">
      <a-input v-model={formState.deptName} />
    </a-form-item>
    <a-form-item name="status" label="部门状态" placeholder="部门状态">
      <a-select v-model={formState.status} options={selectOptions} />
    </a-form-item>
  </>
}} />
```

## 页面效果
![img.png](img.png)

## 参数说明 
### options: 表单配置项
###### [详情参考官方API](https://antdv.com/components/form-cn#API)
```javascript
// Example
options: {
  name: 'deptForm'
}
```
