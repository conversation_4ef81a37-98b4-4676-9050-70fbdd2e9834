/**
 * 基础表格组件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/10/8 17:02:45
 * LastEditors: miaozhendong <EMAIL>
 * LastEditTime: 2024-05-10
 */
import { defineComponent, PropType, computed } from 'vue'
import { uniqueId } from 'lodash'
import { PrimaryTableCol } from 'tdesign-vue-next';

interface PageState {
  pageSize: number;
  pageCurrent: number,
  sortField: string,
  sortOrder: string,
}

export default defineComponent({
  name: 'BaseTable',
  props: {
    // 列表数据
    data: {
      type: Array as PropType<object[]>,
      default: () => {
        return []
      }
    },
    // 表格内容
    columns: {
      type: Array as PropType<object[]>,
      default: () => {
        return []
      }
    },
    // 每页显示条目个数
    pageSize: {
      type: Number,
      default: 10
    },
    // 每页显示个数选择器的选项设置
    pageSizes: {
      type: Array,
      default: () => {
        return [10, 20, 30, 40, 50, 100]
      }
    },
    // 当前页数
    pageCurrent: {
      type: Number,
      default: 1
    },
    // 总条数
    pageTotal: {
      type: Number,
      default: 0
    },
    // 局部Loading
    loading: {
      type: Boolean,
      default: false
    },
    // 属性配置项
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  // 注册回调事件
  emits: ['paginationChange', 'expandedTreeNodesChange'],
  setup(props, { emit }) {
    // 表格列配置项
    const columnSource = computed(() => {
      return props.columns?.map((value: Record<string, any>) => {
        const base = {
          key: value.dataIndex || uniqueId()
        }
        if (value.dataIndex) {
          return Object.assign(base, {
            // 排序
            sorter: (a: Record<string, any>, b: Record<string, any>) => {
              if (typeof a[value.dataIndex] === 'number') {
                return a[value.dataIndex] - b[value.dataIndex]
              }
              return a[value.dataIndex].charCodeAt() - b[value.dataIndex].charCodeAt()
            }
          }, value)
        }
        return Object.assign(base, value)
      })
    })

    const handleTableChange = (
      page: { pageSize: number; current: number },
      filters: PrimaryTableCol[],
      sorter: {
        field: string;
        order: 'ascend' | 'descend';
      }
    ) => {
      emit('paginationChange', {
        pageSize: page.pageSize,
        pageCurrent: page.current,
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...filters
      } as PageState)
    }

    return () => <div class="index-container">
      <t-enhanced-table
        rowKey="id"
        columns={columnSource.value}
        data={props.data}
        loading={props.loading}
        {...props.options}
        onChange={handleTableChange}
      >
      </t-enhanced-table>
    </div>
  }
})
