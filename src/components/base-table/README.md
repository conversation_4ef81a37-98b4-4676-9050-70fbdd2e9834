# 基础表格组件
- 基于Ant Table组件封装
- 全局注册，页面模板可直接使用
- 非引用对象组件参数支持动态注入

## 代码演示
```html
<BaseTable
  options={tableData.options}
  data={tableData.data}
  columns={tableData.columns}
/>
```

### options: 组件配置数据对象
###### 非props列举参数可通过此对象注入
###### [详情参考官方API](https://antdv.com/components/table-cn#Table)
```javascript
// Example
options: {
  rowKey: 'guid', // 唯一主键
  defaultExpandedRowKeys: [guid], // 默认展开的行
  // 自定义class名
  rowClassName: (_record: object, index: number) => {
    return index % 2 === 1 ?   'row-striped' : null
  }
}
```

### columns: 表头数据对象
###### [详情参考官方API](https://antdv.com/components/table-cn#Column)
```javascript
// Example
columns: [
  {
    title: '名称',
    dataIndex: 'name',
    sorter: null
  },
  {
    title: '年龄',
    dataIndex: 'age',
    sorter: null
  }
]
```
