import 'github-markdown-css/github-markdown.css';
import 'katex/dist/katex.min.css';
import './index.less';

import hljs from 'highlight.js';
import MarkdownIt from 'markdown-it';
import markdownItHighlightjs from 'markdown-it-highlightjs';
import markdownItKatex from 'markdown-it-katex';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, defineComponent, onMounted, onUnmounted, ref, watch } from 'vue';

import { useSettingStore } from '@/store';

export interface MarkdownComponentProps {
  /** Markdown 内容 */
  content?: string;
  /** Markdown 文件路径 */
  filePath?: string;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 强制主题模式 */
  theme?: 'light' | 'dark' | 'auto';
}

const MarkdownComponent = defineComponent({
  name: 'MarkdownComponent',
  props: {
    content: {
      type: String,
      default: '',
    },
    filePath: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: '',
    },
    theme: {
      type: String as () => 'light' | 'dark' | 'auto',
      default: 'auto',
    },
  },
  emits: ['update:loading', 'theme-change'],
  setup(props, { emit }) {
    const markdownContent = ref('');
    const isLoading = ref(false);
    const currentTheme = ref<'light' | 'dark'>('light');
    const highlightStyleElement = ref<HTMLStyleElement | null>(null);

    // 获取设置 store
    const settingStore = useSettingStore();

    // 获取页面配置的主题模式
    const getPageTheme = (): 'light' | 'dark' => {
      return settingStore.displayMode;
    };

    // 动态加载代码高亮样式
    const loadHighlightStyle = async (theme: 'light' | 'dark') => {
      // 移除之前的样式
      if (highlightStyleElement.value) {
        highlightStyleElement.value.remove();
        highlightStyleElement.value = null;
      }

      try {
        // 创建新的样式元素
        const styleElement = document.createElement('style');
        styleElement.setAttribute('data-highlight-theme', theme);

        // 动态导入样式内容
        let cssContent = '';
        if (theme === 'dark') {
          // 导入深色主题样式
          const { default: darkStyle } = await import('highlight.js/styles/github-dark.css?inline');
          cssContent = darkStyle;
        } else {
          // 导入浅色主题样式
          const { default: lightStyle } = await import('highlight.js/styles/github.css?inline');
          cssContent = lightStyle;
        }

        // 添加优先级和作用域
        cssContent = cssContent.replace(/\.hljs/g, '.markdown-wrapper .hljs');
        styleElement.textContent = cssContent;
        document.head.appendChild(styleElement);
        highlightStyleElement.value = styleElement;

        console.log(`✅ 代码高亮样式加载成功: ${theme} 模式`);
      } catch (error) {
        console.warn('⚠️ 无法动态加载代码高亮样式，使用内置样式:', error);
        // 降级处理：直接设置基础样式
        const fallbackStyle = document.createElement('style');
        fallbackStyle.setAttribute('data-highlight-fallback', theme);
        fallbackStyle.textContent =
          theme === 'dark'
            ? `
          .markdown-wrapper .hljs {
            color: #e6edf3 !important;
            background: #161b22 !important;
          }
          .markdown-wrapper .hljs-comment { color: #8b949e !important; }
          .markdown-wrapper .hljs-keyword { color: #ff7b72 !important; }
          .markdown-wrapper .hljs-string { color: #a5d6ff !important; }
          .markdown-wrapper .hljs-number { color: #79c0ff !important; }
          .markdown-wrapper .hljs-built_in { color: #ffa657 !important; }
          .markdown-wrapper .hljs-function { color: #d2a8ff !important; }
          .markdown-wrapper .hljs-variable { color: #ffa657 !important; }
          .markdown-wrapper .hljs-attr { color: #79c0ff !important; }
          .markdown-wrapper .hljs-type { color: #ffa657 !important; }
        `
            : `
          .markdown-wrapper .hljs {
            color: #24292f !important;
            background: #f6f8fa !important;
          }
          .markdown-wrapper .hljs-comment { color: #6a737d !important; }
          .markdown-wrapper .hljs-keyword { color: #d73a49 !important; }
          .markdown-wrapper .hljs-string { color: #032f62 !important; }
          .markdown-wrapper .hljs-number { color: #005cc5 !important; }
          .markdown-wrapper .hljs-built_in { color: #e36209 !important; }
          .markdown-wrapper .hljs-function { color: #6f42c1 !important; }
          .markdown-wrapper .hljs-variable { color: #e36209 !important; }
          .markdown-wrapper .hljs-attr { color: #005cc5 !important; }
          .markdown-wrapper .hljs-type { color: #d73a49 !important; }
          .markdown-wrapper .hljs-title { color: #6f42c1 !important; }
          .markdown-wrapper .hljs-tag { color: #22863a !important; }
          .markdown-wrapper .hljs-name { color: #22863a !important; }
          .markdown-wrapper .hljs-selector-id { color: #6f42c1 !important; }
          .markdown-wrapper .hljs-selector-class { color: #6f42c1 !important; }
          .markdown-wrapper .hljs-meta { color: #6a737d !important; }
          .markdown-wrapper .hljs-doctag { color: #6a737d !important; }
          .markdown-wrapper .hljs-literal { color: #005cc5 !important; }
          .markdown-wrapper .hljs-symbol { color: #005cc5 !important; }
          .markdown-wrapper .hljs-link { color: #032f62 !important; }
        `;
        document.head.appendChild(fallbackStyle);
        highlightStyleElement.value = fallbackStyle;

        console.log(`📝 使用内置代码高亮样式: ${theme} 模式`);
      }
    };

    // 更新主题
    const updateTheme = async (newTheme: 'light' | 'dark') => {
      if (currentTheme.value !== newTheme) {
        currentTheme.value = newTheme;
        await loadHighlightStyle(newTheme);

        // 重新高亮所有代码块
        setTimeout(() => {
          hljs.highlightAll();
        }, 100);

        emit('theme-change', newTheme);
      }
    };

    // 页面主题变化监听器 - 监听 store 中的主题变化
    const setupThemeListener = () => {
      // 监听 settingStore 的 displayMode 变化
      watch(
        () => settingStore.displayMode,
        (newDisplayMode) => {
          if (props.theme === 'auto') {
            updateTheme(newDisplayMode);
          }
        },
        { immediate: false },
      );
    };

    // 监听主题属性变化
    watch(
      () => props.theme,
      (newTheme) => {
        if (newTheme === 'auto') {
          updateTheme(getPageTheme());
        } else {
          updateTheme(newTheme);
        }
      },
      { immediate: true },
    );

    // 配置 markdown-it
    const md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      breaks: true,
    })
      .use(markdownItHighlightjs, {
        hljs,
        auto: true,
        code: true,
      })
      .use(markdownItKatex, {
        throwOnError: false,
        errorColor: '#cc0000',
      });

    // 渲染的 HTML 内容
    const renderedHtml = computed(() => {
      if (!markdownContent.value) return '';
      return md.render(markdownContent.value);
    });

    // 计算样式类名
    const wrapperClass = computed(() => {
      const classes = ['markdown-wrapper', `theme-${currentTheme.value}`, props.className].filter(Boolean);
      return classes.join(' ');
    });

    // 加载 Markdown 文件
    const loadMarkdownFile = async (path: string) => {
      if (!path) return;

      isLoading.value = true;
      emit('update:loading', true);

      try {
        const response = await fetch(path);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const content = await response.text();
        markdownContent.value = content;
        // MessagePlugin.success('Markdown 文件加载成功');
      } catch (error) {
        console.error('Failed to load markdown file:', error);
        MessagePlugin.error(`加载 Markdown 文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
        markdownContent.value = '';
      } finally {
        isLoading.value = false;
        emit('update:loading', false);
      }
    };

    // 监听文件路径变化
    watch(
      () => props.filePath,
      (newPath) => {
        if (newPath) {
          loadMarkdownFile(newPath);
        }
      },
      { immediate: true },
    );

    // 监听内容变化
    watch(
      () => props.content,
      (newContent) => {
        if (newContent !== undefined) {
          markdownContent.value = newContent;
        }
      },
      { immediate: true },
    );

    // 监听渲染内容变化，重新高亮代码
    watch(renderedHtml, () => {
      setTimeout(() => {
        hljs.highlightAll();
      }, 100);
    });

    // 组件挂载
    onMounted(() => {
      setupThemeListener();

      // 初始化主题
      const initialTheme = props.theme === 'auto' ? getPageTheme() : props.theme;
      updateTheme(initialTheme);
    });

    // 组件卸载
    onUnmounted(() => {
      if (highlightStyleElement.value) {
        highlightStyleElement.value.remove();
      }
    });

    return () => (
      <div class={wrapperClass.value} data-theme={currentTheme.value}>
        {(isLoading.value || props.loading) && (
          <div class="markdown-loading">
            <t-loading size="large" text="正在加载 Markdown 内容..." />
          </div>
        )}
        {!isLoading.value && !props.loading && renderedHtml.value && (
          <div
            class="markdown-body"
            style={{
              boxSizing: 'border-box',
              minWidth: '200px',
              margin: '0 auto',
              padding: '24px',
              fontSize: '16px',
              lineHeight: 1.6,
              wordWrap: 'break-word',
            }}
            innerHTML={renderedHtml.value}
          />
        )}
        {!isLoading.value && !props.loading && !renderedHtml.value && (
          <div class="markdown-empty">
            <t-empty description="暂无 Markdown 内容" />
          </div>
        )}
      </div>
    );
  },
});

export default MarkdownComponent;
