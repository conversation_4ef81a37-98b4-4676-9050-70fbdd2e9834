// CSS 变量定义 - 浅色主题
:root {
  // 背景色
  --md-bg-primary: #ffffff;
  --md-bg-secondary: #f6f8fa;
  --md-bg-tertiary: #f1f3f4;
  
  // 文字颜色
  --md-text-primary: #24292f;
  --md-text-secondary: #656d76;
  --md-text-tertiary: #8b949e;
  
  // 边框颜色
  --md-border-primary: #d0d7de;
  --md-border-secondary: #e1e4e8;
  
  // 链接颜色
  --md-link-primary: #0969da;
  --md-link-hover: #0550ae;
  
  // 代码颜色
  --md-code-bg: rgba(175, 184, 193, 0.2);
  --md-code-text: #e36209;
  --md-code-block-bg: #f6f8fa;
  --md-code-block-border: #e1e4e8;
  
  // 表格专用颜色
  --md-table-header-bg: #24292f;
  --md-table-header-text: #ffffff;
  --md-table-row-even-bg: #f6f8fa;
  --md-table-row-hover-bg: #eef2f5;
  
  // 阴影
  --md-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --md-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  // 高亮颜色
  --md-highlight-bg: #fff3cd;
  --md-highlight-border: #ffeaa7;
}

// 深色主题 - 跟随页面配置的 theme-mode 属性
:root[theme-mode="dark"] {
  // 背景色
  --md-bg-primary: #0d1117;
  --md-bg-secondary: #161b22;
  --md-bg-tertiary: #21262d;
  
  // 文字颜色
  --md-text-primary: #e6edf3;
  --md-text-secondary: #8b949e;
  --md-text-tertiary: #6e7681;
  
  // 边框颜色
  --md-border-primary: #30363d;
  --md-border-secondary: #21262d;
  
  // 链接颜色
  --md-link-primary: #58a6ff;
  --md-link-hover: #79c0ff;
  
  // 代码颜色
  --md-code-bg: rgba(110, 118, 129, 0.4);
  --md-code-text: #ffa657;
  --md-code-block-bg: #161b22;
  --md-code-block-border: #30363d;
  
  // 表格专用颜色
  --md-table-header-bg: #21262d;
  --md-table-header-text: #e6edf3;
  --md-table-row-even-bg: #161b22;
  --md-table-row-hover-bg: #30363d;
  
  // 阴影
  --md-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --md-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.4);
  
  // 高亮颜色
  --md-highlight-bg: #fffbdd;
  --md-highlight-border: #d4a72c;
}

.markdown-wrapper {
  width: 100%;
  color: var(--md-text-primary);
  transition: color 0.3s ease, background-color 0.3s ease;
  
  .markdown-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    color: var(--md-text-secondary);
  }
  
  .markdown-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    color: var(--md-text-secondary);
  }
  
  .markdown-body {
    background-color: var(--md-bg-primary);
    border-radius: 8px;
    box-shadow: var(--md-shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    color: var(--md-text-primary);
    
    &:hover {
      box-shadow: var(--md-shadow-hover);
    }
    
    // 自定义代码块样式
    pre {
      background-color: var(--md-code-block-bg) !important;
      border-radius: 6px;
      padding: 16px;
      overflow-x: auto;
      border: 1px solid var(--md-code-block-border);
      transition: background-color 0.3s ease, border-color 0.3s ease;
      
      code {
        background-color: transparent !important;
        padding: 0 !important;
        border-radius: 0 !important;
        font-size: 14px;
        line-height: 1.5;
        color: var(--md-text-primary);
      }
    }
    
    // 行内代码样式
    code:not(pre code) {
      background-color: var(--md-code-bg) !important;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 0.9em;
      color: var(--md-code-text);
      transition: background-color 0.3s ease, color 0.3s ease;
    }
    
    // 表格样式增强
    table {
      width: 100%;
      margin: 16px 0;
      border-collapse: collapse;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: var(--md-shadow);
      
      th,
      td {
        padding: 12px 16px;
        text-align: left;
        border: 1px solid var(--md-border-primary);
        transition: all 0.3s ease;
      }
      
      th {
        background-color: var(--md-table-header-bg);
        font-weight: 600;
        color: var(--md-table-header-text);
        border-color: var(--md-table-header-bg);
        font-size: 14px;
        letter-spacing: 0.5px;
        text-transform: uppercase;
      }
      
      tbody tr {
        background-color: var(--md-bg-primary);
        
        &:nth-child(even) {
          background-color: var(--md-table-row-even-bg);
        }
        
        &:hover {
          background-color: var(--md-table-row-hover-bg);
          transform: translateY(-1px);
          box-shadow: var(--md-shadow-hover);
        }
      }
      
      td {
        color: var(--md-text-primary);
        border-color: var(--md-border-secondary);
      }
    }
    
    // 引用样式
    blockquote {
      border-left: 4px solid var(--md-border-primary);
      padding: 0 16px;
      margin: 16px 0;
      color: var(--md-text-secondary);
      background-color: var(--md-bg-secondary);
      border-radius: 0 6px 6px 0;
      transition: all 0.3s ease;
      
      p:first-child {
        margin-top: 0;
      }
      
      p:last-child {
        margin-bottom: 0;
      }
      
      // 嵌套引用
      blockquote {
        border-left-color: var(--md-text-tertiary);
        background-color: var(--md-bg-tertiary);
      }
    }
    
    // 链接样式
    a {
      color: var(--md-link-primary);
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--md-link-hover);
        text-decoration: underline;
      }
      
      &:visited {
        color: var(--md-text-secondary);
      }
    }
    
    // 水平分割线
    hr {
      border: none;
      height: 1px;
      background-color: var(--md-border-primary);
      margin: 24px 0;
      transition: background-color 0.3s ease;
    }
    
    // 列表样式
    ul,
    ol {
      padding-left: 24px;
      color: var(--md-text-primary);
      
      li {
        margin: 4px 0;
        transition: color 0.3s ease;
        
        &::marker {
          color: var(--md-text-secondary);
        }
      }
      
      // 嵌套列表
      ul, ol {
        margin: 8px 0;
      }
    }
    
    // 任务列表样式
    ul {
      li {
        list-style: none;
        position: relative;
        
        input[type="checkbox"] {
          margin-right: 8px;
          accent-color: var(--md-link-primary);
        }
      }
    }
    
    // 标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
      color: var(--md-text-primary);
      transition: color 0.3s ease, border-color 0.3s ease;
      
      &:first-child {
        margin-top: 0;
      }
      
      // 标题锚点样式
      &:hover {
        .anchor {
          opacity: 1;
        }
      }
      
      .anchor {
        opacity: 0;
        transition: opacity 0.3s ease;
        color: var(--md-text-tertiary);
        text-decoration: none;
        margin-left: 8px;
        
        &:hover {
          color: var(--md-link-primary);
        }
      }
    }
    
    h1 {
      font-size: 2em;
      border-bottom: 1px solid var(--md-border-primary);
      padding-bottom: 10px;
    }
    
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid var(--md-border-primary);
      padding-bottom: 8px;
    }
    
    h3 {
      font-size: 1.25em;
    }
    
    h4 {
      font-size: 1em;
    }
    
    h5 {
      font-size: 0.875em;
    }
    
    h6 {
      font-size: 0.85em;
    }
    
    // 图片样式
    img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      box-shadow: var(--md-shadow);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      
      &:hover {
        transform: scale(1.02);
        box-shadow: var(--md-shadow-hover);
      }
    }
    
    // 段落样式
    p {
      margin: 0 0 16px;
      line-height: 1.6;
    }
    
    // 强调文本
    strong,
    b {
      font-weight: 600;
      color: var(--md-text-primary);
    }
    
    // 斜体文本
    em,
    i {
      font-style: italic;
      color: var(--md-text-secondary);
    }
    
    // 删除线文本
    del,
    s {
      text-decoration: line-through;
      color: var(--md-text-tertiary);
    }
    
    // 高亮文本
    mark {
      background-color: var(--md-highlight-bg);
      border: 1px solid var(--md-highlight-border);
      padding: 2px 4px;
      border-radius: 3px;
      color: var(--md-text-primary);
    }
    
    // 键盘按键样式
    kbd {
      display: inline-block;
      padding: 3px 5px;
      font-size: 11px;
      line-height: 10px;
      color: var(--md-text-primary);
      vertical-align: middle;
      background-color: var(--md-bg-secondary);
      border: solid 1px var(--md-border-primary);
      border-bottom-color: var(--md-border-secondary);
      border-radius: 3px;
      box-shadow: inset 0 -1px 0 var(--md-border-secondary);
    }
    
    // 上标和下标
    sup,
    sub {
      font-size: 0.8em;
      line-height: 0;
      position: relative;
      vertical-align: baseline;
    }
    
    sup {
      top: -0.5em;
    }
    
    sub {
      bottom: -0.25em;
    }
  }
}

// 浅色模式代码高亮样式
.markdown-wrapper .hljs-comment { color: #6a737d; }
.markdown-wrapper .hljs-keyword { color: #d73a49; }
.markdown-wrapper .hljs-string { color: #032f62; }
.markdown-wrapper .hljs-number { color: #005cc5; }
.markdown-wrapper .hljs-built_in { color: #e36209; }
.markdown-wrapper .hljs-function { color: #6f42c1; }
.markdown-wrapper .hljs-variable { color: #e36209; }
.markdown-wrapper .hljs-attr { color: #005cc5; }
.markdown-wrapper .hljs-type { color: #d73a49; }
.markdown-wrapper .hljs-title { color: #6f42c1; }
.markdown-wrapper .hljs-tag { color: #22863a; }
.markdown-wrapper .hljs-name { color: #22863a; }
.markdown-wrapper .hljs-selector-id { color: #6f42c1; }
.markdown-wrapper .hljs-selector-class { color: #6f42c1; }
.markdown-wrapper .hljs-meta { color: #6a737d; }
.markdown-wrapper .hljs-doctag { color: #6a737d; }
.markdown-wrapper .hljs-literal { color: #005cc5; }
.markdown-wrapper .hljs-symbol { color: #005cc5; }
.markdown-wrapper .hljs-link { color: #032f62; }

// 深色主题代码高亮样式
:root[theme-mode="dark"] .markdown-wrapper .hljs-comment { color: #8b949e; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-keyword { color: #ff7b72; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-string { color: #a5d6ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-number { color: #79c0ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-built_in { color: #ffa657; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-function { color: #d2a8ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-variable { color: #ffa657; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-attr { color: #79c0ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-type { color: #ffa657; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-title { color: #d2a8ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-tag { color: #7ee787; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-name { color: #7ee787; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-selector-id { color: #d2a8ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-selector-class { color: #d2a8ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-meta { color: #8b949e; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-doctag { color: #8b949e; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-literal { color: #79c0ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-symbol { color: #79c0ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-link { color: #a5d6ff; }

// 滚动条样式
.markdown-body {
  // Webkit 浏览器滚动条
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--md-bg-secondary);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--md-border-primary);
    border-radius: 4px;
    transition: background-color 0.3s ease;
    
    &:hover {
      background: var(--md-text-tertiary);
    }
  }
  
  // Firefox 滚动条
  scrollbar-width: thin;
  scrollbar-color: var(--md-border-primary) var(--md-bg-secondary);
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.markdown-wrapper {
  animation: fadeIn 0.5s ease-out;
}

// 打印样式
@media print {
  .markdown-wrapper {
    .markdown-body {
      box-shadow: none;
      border: 1px solid #ccc;
      
      // 打印时使用浅色主题
      background-color: #fff;
      color: #24292f;
      
      pre {
        background-color: #f6f8fa;
        border: 1px solid #e1e4e8;
      }
      
      blockquote {
        background-color: #f6f8fa;
        border-left-color: #d0d7de;
      }
    }
  }
} 