# 🎨 Vue3 TSX Markdown 主题组件

这是一个功能完整的 Vue3 tsx Markdown 渲染组件，支持**浅色/深色主题自动切换**、GitHub 风格样式、代码高亮和数学公式渲染。

## ✨ 主题功能

### 🌓 主题模式
- **自动模式**: 跟随页面配置模块的主题设置
- **浅色模式**: 强制使用浅色主题
- **深色模式**: 强制使用深色主题

### 🔄 主题跟随机制
组件现在与页面的主题配置保持同步：
- 当页面主题配置为"跟随系统"时，组件也会跟随系统主题
- 当页面主题配置为"明亮"或"暗黑"时，组件会跟随页面设置
- 这确保了 Markdown 组件与整个应用的主题保持一致

### 🎨 CSS 变量系统
使用 CSS 变量实现主题切换，支持平滑过渡动画：

```css
:root {
  /* 浅色主题变量 */
  --md-bg-primary: #ffffff;
  --md-text-primary: #24292f;
  --md-link-primary: #0969da;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* 深色主题变量 */
    --md-bg-primary: #0d1117;
    --md-text-primary: #e6edf3;
    --md-link-primary: #58a6ff;
  }
}
```

### 🌈 主题色彩方案

#### 浅色主题 (Light Theme)
- 背景色：白色系 (#ffffff, #f6f8fa)
- 文字色：深色系 (#24292f, #656d76)
- 链接色：蓝色系 (#0969da, #0550ae)
- 代码块：浅灰背景 (#f6f8fa)

#### 深色主题 (Dark Theme)
- 背景色：深色系 (#0d1117, #161b22)
- 文字色：浅色系 (#e6edf3, #8b949e)
- 链接色：亮蓝色 (#58a6ff, #79c0ff)
- 代码块：深灰背景 (#161b22)

## 🚀 使用方法

### 基础用法

```tsx
import MarkdownComponent from '@/components/markdown/Markdown';

// 自动主题模式（跟随页面配置，默认）
<MarkdownComponent content="# Hello World" theme="auto" />

// 强制浅色主题
<MarkdownComponent content="# Hello World" theme="light" />

// 强制深色主题  
<MarkdownComponent content="# Hello World" theme="dark" />
```

### 高级用法

```tsx
import { ref } from 'vue';
import MarkdownComponent from '@/components/markdown/Markdown';

const App = () => {
  const currentTheme = ref<'light' | 'dark' | 'auto'>('auto');
  const markdownContent = ref('# 主题演示\n\n支持与页面主题配置同步！');

  const handleThemeChange = (theme: 'light' | 'dark') => {
    console.log(`主题已切换为: ${theme}`);
  };

  return (
    <MarkdownComponent
      content={markdownContent.value}
      theme={currentTheme.value}
      onTheme-change={handleThemeChange}
      className="custom-markdown"
    />
  );
};
```

## 📦 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `content` | `string` | `''` | Markdown 内容字符串 |
| `filePath` | `string` | `''` | Markdown 文件路径 |
| `loading` | `boolean` | `false` | 是否显示加载状态 |
| `className` | `string` | `''` | 自定义样式类名 |
| **`theme`** | `'light' \| 'dark' \| 'auto'` | `'auto'` | **主题模式（跟随页面配置）** |

## 🔔 组件事件

| 事件名 | 参数类型 | 说明 |
|--------|----------|------|
| `update:loading` | `(loading: boolean)` | 加载状态变化时触发 |
| **`theme-change`** | `(theme: 'light' \| 'dark')` | **主题切换时触发** |

## 🎯 主题特性

### ✅ 完整的主题覆盖
- 背景色和前景色
- 代码块语法高亮
- 表格样式
- 引用块样式
- 链接颜色
- 边框和阴影
- 滚动条样式

### ✅ 平滑过渡动画
所有颜色变化都有 `0.3s ease` 过渡效果：

```css
.markdown-wrapper {
  transition: color 0.3s ease, background-color 0.3s ease;
}
```

### ✅ 页面主题同步
```javascript
// 获取页面配置的主题模式
const getPageTheme = () => {
  return settingStore.displayMode;
};

// 监听页面主题变化
watch(
  () => settingStore.displayMode,
  (newDisplayMode) => {
    if (props.theme === 'auto') {
      updateTheme(newDisplayMode);
    }
  }
);
```

### ✅ 动态代码高亮
根据主题自动切换代码高亮样式：
- 浅色主题：`highlight.js/styles/github.css`
- 深色主题：`highlight.js/styles/github-dark.css`

## 📝 更新说明

### v2.0 主要变更
- **🔄 主题跟随机制变更**: 从跟随系统主题改为跟随页面配置模块的主题模式
- **🎯 更好的一致性**: 确保 Markdown 组件与整个应用的主题保持同步
- **⚡ 响应式更新**: 实时响应页面主题配置的变化
- **🔧 CSS 变量优化**: 修改了 CSS 选择器，从 `@media (prefers-color-scheme: dark)` 改为 `:root[theme-mode="dark"]`
- **🚀 主题初始化**: 添加了应用启动时的主题初始化逻辑

### 🛠️ 技术实现修复

#### 1. CSS 变量系统更新
```css
/* 之前 - 跟随系统主题 */
@media (prefers-color-scheme: dark) {
  :root { /* 深色样式 */ }
}

/* 现在 - 跟随页面配置 */
:root[theme-mode="dark"] {
  /* 深色样式 */
}
```

#### 2. 主题监听机制
```typescript
// 之前 - 监听系统主题
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
mediaQuery.addEventListener('change', handleThemeChange);

// 现在 - 监听页面配置
watch(
  () => settingStore.displayMode,
  (newDisplayMode) => {
    if (props.theme === 'auto') {
      updateTheme(newDisplayMode);
    }
  }
);
```

#### 3. 主题初始化
```typescript
// App.vue 中添加主题初始化
onMounted(() => {
  store.changeMode(store.mode);
});
```

这样确保了 Markdown 组件始终与页面的整体主题保持一致，提供更好的用户体验。

### ✅ 问题解决清单
- [x] Markdown 组件跟随页面配置而非系统主题
- [x] CSS 变量正确响应页面主题设置
- [x] 主题在应用启动时正确初始化
- [x] 调试工具帮助验证主题状态
- [x] 文档更新说明变更内容
- [x] **表格样式优化 - 解决浅色模式下字体对比度问题**
- [x] **代码高亮优化 - 完善浅色模式下的语法高亮显示**

### 🎨 表格样式优化

#### 新增表格专用颜色变量
```css
/* 浅色主题 */
:root {
  --md-table-header-bg: #24292f;      /* 深色表头背景 */
  --md-table-header-text: #ffffff;     /* 白色表头文字 */
  --md-table-row-even-bg: #f6f8fa;     /* 偶数行背景 */
  --md-table-row-hover-bg: #eef2f5;    /* 悬停行背景 */
}

/* 深色主题 */
:root[theme-mode="dark"] {
  --md-table-header-bg: #21262d;       /* 深色表头背景 */
  --md-table-header-text: #e6edf3;     /* 浅色表头文字 */
  --md-table-row-even-bg: #161b22;     /* 偶数行背景 */
  --md-table-row-hover-bg: #30363d;    /* 悬停行背景 */
}
```

#### 表格样式增强
- **高对比度表头**: 浅色模式下使用深色背景 + 白色文字
- **现代化设计**: 圆角边框、阴影效果、悬停动画
- **更好的可读性**: 优化了行间距、字体大小和颜色对比度
- **响应式交互**: 悬停时有轻微的提升效果和阴影变化

### 💡 代码高亮优化

#### 完整的语法高亮支持
现在代码高亮包含了完整的语法元素颜色定义：

**浅色模式 GitHub 风格**:
```css
.markdown-wrapper .hljs-comment { color: #6a737d; }     /* 注释 - 灰色 */
.markdown-wrapper .hljs-keyword { color: #d73a49; }     /* 关键字 - 红色 */
.markdown-wrapper .hljs-string { color: #032f62; }      /* 字符串 - 深蓝 */
.markdown-wrapper .hljs-number { color: #005cc5; }      /* 数字 - 蓝色 */
.markdown-wrapper .hljs-function { color: #6f42c1; }    /* 函数 - 紫色 */
.markdown-wrapper .hljs-variable { color: #e36209; }    /* 变量 - 橙色 */
```

**深色模式 GitHub Dark 风格**:
```css
:root[theme-mode="dark"] .markdown-wrapper .hljs-comment { color: #8b949e; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-keyword { color: #ff7b72; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-string { color: #a5d6ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-number { color: #79c0ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-function { color: #d2a8ff; }
:root[theme-mode="dark"] .markdown-wrapper .hljs-variable { color: #ffa657; }
```

#### 多重保障机制
1. **动态加载优先**: 尝试从 highlight.js 动态加载完整样式
2. **内置样式降级**: 动态加载失败时使用内置的完整样式
3. **CSS 样式兜底**: 提供基础的 CSS 样式作为最后保障
4. **作用域隔离**: 使用 `.markdown-wrapper` 前缀避免样式冲突

#### 支持的语法元素
- 注释 (comments)
- 关键字 (keywords)  
- 字符串 (strings)
- 数字 (numbers)
- 内置函数 (built-in functions)
- 用户函数 (functions)
- 变量 (variables)
- 属性 (attributes)
- 类型 (types)
- 标题 (titles)
- HTML 标签 (tags)
- CSS 选择器 (selectors)
- 元数据 (meta)
- 文档标签 (doctags)
- 字面量 (literals)
- 符号 (symbols)
- 链接 (links)

## 🌟 演示页面功能

访问 `/demo` 查看完整演示，包括：

### 🛠️ 主题控制面板
- 三种主题模式切换
- 实时预览效果
- 内容编辑器

### 📋 功能展示
- 各种 Markdown 语法演示
- 代码高亮效果对比
- 数学公式渲染
- 表格和引用样式

### 🔍 技术实现说明
- CSS 变量系统介绍
- 主题检测原理
- 过渡动画效果

## 🛠️ 技术实现

### CSS 变量定义
```css
:root {
  /* 背景色 */
  --md-bg-primary: #ffffff;
  --md-bg-secondary: #f6f8fa;
  --md-bg-tertiary: #f1f3f4;
  
  /* 文字颜色 */
  --md-text-primary: #24292f;
  --md-text-secondary: #656d76;
  --md-text-tertiary: #8b949e;
  
  /* 其他颜色... */
}

@media (prefers-color-scheme: dark) {
  :root {
    /* 深色主题重写变量 */
    --md-bg-primary: #0d1117;
    --md-text-primary: #e6edf3;
    /* ... */
  }
}
```

### 组件实现关键点
```typescript
// 1. 主题检测
const detectSystemTheme = (): 'light' | 'dark' => {
  return window.matchMedia('(prefers-color-scheme: dark)').matches 
    ? 'dark' : 'light';
};

// 2. 动态样式加载
const loadHighlightStyle = async (theme: 'light' | 'dark') => {
  // 动态加载对应主题的代码高亮样式
};

// 3. 主题监听
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
mediaQuery.addEventListener('change', handleThemeChange);
```

## 🔧 浏览器兼容性

| 特性 | Chrome | Firefox | Safari | Edge |
|------|--------|---------|--------|------|
| CSS 变量 | ✅ 49+ | ✅ 31+ | ✅ 9.1+ | ✅ 16+ |
| prefers-color-scheme | ✅ 76+ | ✅ 67+ | ✅ 12.1+ | ✅ 79+ |
| 媒体查询监听 | ✅ 9+ | ✅ 6+ | ✅ 5.1+ | ✅ 9+ |

## 📝 注意事项

1. **系统主题检测**: 需要现代浏览器支持
2. **CSS 变量**: 确保目标浏览器支持 CSS 自定义属性
3. **性能优化**: 主题切换时避免频繁重绘
4. **无障碍访问**: 保持良好的颜色对比度

## 🎉 总结

这个增强版的 Markdown 组件现在支持：
- 🌓 自动主题检测和切换
- 🎨 完整的 CSS 变量系统
- 🌈 美观的深色/浅色主题
- ⚡ 平滑的过渡动画
- 🔧 灵活的配置选项

非常适合现代 Web 应用的主题需求！ 