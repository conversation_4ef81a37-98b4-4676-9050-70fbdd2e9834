<template>
  <result title="403 无权限" tip="抱歉，您无权限访问此页面，请联系管理员">
    <t-space>
      <t-button @click="() => $router.push('/')">返回首页</t-button>
      <t-button @click="() => $router.push('/login')">返回登录页</t-button>
    </t-space>
  </result>
</template>
<script lang="ts">
export default {
  name: 'Result403',
};
</script>
<script setup lang="ts">
import Result from '@/components/result/index.vue';
</script>
