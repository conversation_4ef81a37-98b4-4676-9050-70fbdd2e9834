<template>
  <result title="404 Not Found" :tip="$t('pages.result.404.subtitle')" type="404">
    <t-space>
      <t-button @click="() => $router.push('/')">返回首页</t-button>
      <t-button @click="() => $router.push('/login')">返回登录页</t-button>
    </t-space>
  </result>
</template>

<script lang="ts">
export default {
  name: 'Result404',
};
</script>

<script setup lang="ts">
import Result from '@/components/result/index.vue';
</script>
