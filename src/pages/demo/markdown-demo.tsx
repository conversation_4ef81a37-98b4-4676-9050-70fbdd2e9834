import { Button, Card, Divider, MessagePlugin, Radio, Space, Textarea } from 'tdesign-vue-next';
import { computed, defineComponent, ref } from 'vue';

import { useSettingStore } from '@/store';

import MarkdownComponent from '../../components/markdown';

const MarkdownDemo = defineComponent({
  name: 'MarkdownDemo',
  setup() {
    const activeMode = ref<'file' | 'content'>('file');
    const markdownContent = ref('');
    const currentTheme = ref<'light' | 'dark' | 'auto'>('auto');
    const isLoading = ref(false);
    const customContent = ref(`# 自定义 Markdown 内容

这是一个可编辑的 Markdown 示例，支持**浅色/深色主题**自动切换。

## 🎨 主题功能演示

组件支持三种主题模式：
- **自动模式**: 跟随系统主题设置
- **浅色模式**: 强制使用浅色主题  
- **深色模式**: 强制使用深色主题

## 代码高亮示例

### JavaScript/TypeScript
\`\`\`javascript
// Vue 3 响应式示例
import { ref, computed, watch } from 'vue';

const count = ref(0);
const doubled = computed(() => count.value * 2);

watch(count, (newValue, oldValue) => {
  console.log(\`Count changed from \${oldValue} to \${newValue}\`);
});

// 主题切换函数
const toggleTheme = () => {
  document.documentElement.setAttribute(
    'data-theme', 
    document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark'
  );
};
\`\`\`

### Python 数据科学
\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 创建示例数据
data = {
    'theme': ['light', 'dark', 'auto'],
    'popularity': [85, 92, 78],
    'satisfaction': [4.2, 4.7, 4.1]
}

df = pd.DataFrame(data)

# 绘制主题使用情况
plt.figure(figsize=(10, 6))
plt.bar(df['theme'], df['popularity'], alpha=0.7)
plt.title('主题模式使用情况统计')
plt.xlabel('主题模式')
plt.ylabel('使用率 (%)')
plt.show()
\`\`\`

### CSS 主题变量
\`\`\`css
/* 主题变量定义 */
:root {
  --bg-primary: #ffffff;
  --text-primary: #24292f;
  --accent-color: #0969da;
}

@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0d1117;
    --text-primary: #e6edf3;
    --accent-color: #58a6ff;
  }
}

.themed-component {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}
\`\`\`

## 数学公式渲染

### 行内公式
主题切换不影响数学公式的渲染：$E = mc^2$，$\\alpha + \\beta = \\gamma$

### 块级公式
傅里叶变换公式：
$$
\\mathcal{F}\\{f(t)\\} = \\int_{-\\infty}^{\\infty} f(t) e^{-2\\pi i \\xi t} dt
$$

薛定谔方程：
$$
i\\hbar\\frac{\\partial}{\\partial t}\\Psi(x,t) = \\hat{H}\\Psi(x,t)
$$

## 表格 - 主题对比

| 特性 | 浅色主题 | 深色主题 | 自动模式 |
|------|----------|----------|----------|
| 背景色 | 白色系 | 黑色系 | 跟随系统 |
| 文字对比度 | 高 | 高 | 自适应 |
| 眼部舒适度 | 日间最佳 | 夜间友好 | 全天候 |
| 代码高亮 | GitHub Light | GitHub Dark | 自动切换 |
| 电池续航 | 一般 | 更好 (OLED) | 智能优化 |

## 任务列表

- [x] 实现基础Markdown渲染
- [x] 添加代码语法高亮
- [x] 集成数学公式支持
- [x] 实现主题自动切换
- [x] 优化深色模式体验
- [ ] 添加更多自定义主题
- [ ] 支持主题配色定制

## 引用与提示

> **💡 提示**: 主题切换功能使用 \`prefers-color-scheme\` 媒体查询实现，
> 能够自动检测用户的系统主题偏好设置。

> **⚠️ 注意**: 在某些旧版本浏览器中，主题切换功能可能无法正常工作，
> 建议使用现代浏览器以获得最佳体验。

## 链接测试

- [Vue.js 官方文档](https://vuejs.org/)
- [TDesign 组件库](https://tdesign.tencent.com/)
- [GitHub Markdown CSS](https://github.com/sindresorhus/github-markdown-css)
- [Highlight.js 官网](https://highlightjs.org/)

---

**尝试切换上方的主题模式，观察页面样式的实时变化！** ✨`);

    const modeOptions = [
      { label: '从文件加载', value: 'file' },
      { label: '自定义内容', value: 'content' },
    ];

    const themeOptions = [
      { label: '自动模式', value: 'auto' },
      { label: '浅色主题', value: 'light' },
      { label: '深色主题', value: 'dark' },
    ];

    const loadSampleFile = () => {
      markdownContent.value = '/demo/sample.md';
      MessagePlugin.info('正在加载示例文件...');
    };

    const clearContent = () => {
      if (activeMode.value === 'file') {
        markdownContent.value = '';
      } else {
        customContent.value = '';
      }
      MessagePlugin.info('内容已清空');
    };

    const resetContent = () => {
      if (activeMode.value === 'file') {
        loadSampleFile();
      } else {
        customContent.value = `# 重置内容

这是重置后的**主题测试**内容。

## 快速主题测试

\`\`\`typescript
interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  colors: {
    primary: string;
    background: string;
    text: string;
  };
}

const themeConfig: ThemeConfig = {
  mode: 'auto',
  colors: {
    primary: 'var(--md-link-primary)',
    background: 'var(--md-bg-primary)', 
    text: 'var(--md-text-primary)'
  }
};
\`\`\`

### 数学公式测试
二次方程求根公式：$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$

### 表格测试
| 模式 | 状态 |
|------|------|
| Light | ✅ |
| Dark | ✅ |
| Auto | ✅ |

**内容已重置，主题功能正常！** 🎉`;
      }
      MessagePlugin.success('内容已重置');
    };

    // 修正：theme参数应为 'light' | 'dark' | 'auto'
    const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
      if (theme === 'light') {
        MessagePlugin.success('主题已切换为: 浅色模式');
      } else if (theme === 'dark') {
        MessagePlugin.success('主题已切换为: 深色模式');
      } else {
        MessagePlugin.success('主题已切换为: 自动模式');
      }
    };

    const insertSampleCode = () => {
      const sampleCode = `

## 新增代码示例

\`\`\`vue
<template>
  <div class="theme-demo">
    <h1>{{ title }}</h1>
    <button @click="toggleTheme">切换主题</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useSettingStore } from '@/store';

const title = ref('主题演示');
const settingStore = useSettingStore();

// 现在主题跟随页面配置模块
const toggleTheme = () => {
  const currentMode = settingStore.mode;
  const newMode = currentMode === 'light' ? 'dark' : 'light';
  settingStore.updateConfig({ mode: newMode });
};

// 获取当前主题
const currentTheme = computed(() => settingStore.displayMode);
</script>
\`\`\`
`;
      if (activeMode.value === 'content') {
        customContent.value += sampleCode;
        MessagePlugin.success('已插入示例代码');
      } else {
        MessagePlugin.warning('请切换到自定义内容模式');
      }
    };

    // 添加测试页面主题跟随的函数
    const testPageThemeSync = () => {
      MessagePlugin.info('点击右上角的设置按钮来测试主题跟随功能！Markdown组件会自动跟随页面主题配置的变化。');
    };

    // 获取设置 store 用于调试
    const settingStore = useSettingStore();

    // 添加调试信息函数
    const showDebugInfo = () => {
      const debugInfo = {
        页面配置模式: settingStore.mode,
        计算后的显示模式: settingStore.displayMode,
        系统主题偏好: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
        'HTML根元素theme-mode属性': document.documentElement.getAttribute('theme-mode') || '未设置',
        Markdown组件当前主题: currentTheme.value,
      };

      let message = '🔍 主题调试信息:\n';
      for (const [key, value] of Object.entries(debugInfo)) {
        message += `${key}: ${value}\n`;
      }

      MessagePlugin.info({
        content: message,
        duration: 8000,
      });
    };

    return () => (
      <div class="markdown-demo-page" style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px' }}>
          <h1 style={{ color: 'var(--md-text-secondary)', fontSize: '28px', fontWeight: 'bold', marginBottom: '8px' }}>
            🎨 Markdown 主题组件演示
          </h1>
          <p style={{ color: 'var(--md-text-secondary)', fontSize: '16px' }}>
            功能完整的 Vue3 tsx Markdown 渲染组件，支持浅色/深色主题自动切换、代码高亮和数学公式渲染。
          </p>
        </div>

        <Card title="🛠️ 配置选项" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <h4 style={{ marginBottom: '12px', color: 'var(--md-text-primary)' }}>内容模式</h4>
              {/* 修正：Radio.Group 需使用 modelValue/onUpdate:modelValue 绑定 */}
              <Radio.Group
                modelValue={activeMode.value}
                options={modeOptions}
                onUpdate:modelValue={(val) => (activeMode.value = val)}
              />
            </div>

            <Divider />

            <div>
              <h4 style={{ marginBottom: '12px', color: 'var(--md-text-primary)' }}>主题模式</h4>
              <Radio.Group
                modelValue={currentTheme.value}
                options={themeOptions}
                onUpdate:modelValue={(val) => (currentTheme.value = val)}
              />
              <p
                style={{
                  fontSize: '12px',
                  color: 'var(--md-text-tertiary)',
                  marginTop: '8px',
                  lineHeight: 1.4,
                }}
              >
                💡 自动模式会跟随页面配置模块的主题设置，确保与整个应用主题保持一致
              </p>
            </div>

            <Divider />

            <Space>
              <Button theme="primary" onClick={activeMode.value === 'file' ? loadSampleFile : resetContent}>
                {activeMode.value === 'file' ? '📄 加载示例文件' : '🔄 重置内容'}
              </Button>
              <Button theme="default" onClick={clearContent}>
                🗑️ 清空内容
              </Button>
              {activeMode.value === 'content' && (
                <Button theme="default" onClick={insertSampleCode}>
                  ➕ 插入代码示例
                </Button>
              )}
              <Button theme="warning" onClick={showDebugInfo}>
                🔍 调试主题状态
              </Button>
            </Space>
          </Space>
        </Card>

        {activeMode.value === 'content' && (
          <div style={{ display: 'flex', gap: '24px', marginBottom: '24px' }}>
            <Card title="✏️ 编辑区域" style={{ flex: 1 }}>
              {/* 修正：Textarea 需使用 modelValue/onUpdate:modelValue 绑定 */}
              <Textarea
                modelValue={customContent.value}
                onUpdate:modelValue={(val) => (customContent.value = val)}
                placeholder="在这里输入 Markdown 内容..."
                style={{ height: '500px', resize: 'vertical' }}
                autosize
              />
            </Card>

            <Card title="👀 实时预览" style={{ flex: 1 }}>
              <div
                style={{
                  height: '500px',
                  overflow: 'auto',
                  border: '1px solid var(--md-border-primary)',
                  borderRadius: '6px',
                  transition: 'border-color 0.3s ease',
                }}
              >
                <MarkdownComponent
                  content={customContent.value}
                  theme={currentTheme.value}
                  onTheme-change={handleThemeChange}
                />
              </div>
            </Card>
          </div>
        )}

        <Card title="🎨 渲染结果">
          <MarkdownComponent
            filePath={activeMode.value === 'file' ? markdownContent.value : undefined}
            content={activeMode.value === 'content' ? customContent.value : undefined}
            theme={currentTheme.value}
            className="demo-markdown"
            loading={isLoading.value}
            // 修正：onUpdate:loading 需使用 onUpdate:loading prop
            onUpdate:loading={(loading) => (isLoading.value = loading)}
            onTheme-change={handleThemeChange}
          />
        </Card>

        <Card title="📋 功能特性">
          <div style={{ lineHeight: 1.8 }}>
            <h3 style={{ marginBottom: '16px', color: 'var(--md-text-primary)' }}>✨ 核心功能</h3>
            <ul style={{ paddingLeft: '20px', marginBottom: '20px', color: 'var(--md-text-primary)' }}>
              <li>
                <strong>🎨 主题自适应</strong>：支持浅色、深色、自动三种模式
              </li>
              <li>
                <strong>🌈 GitHub 风格样式</strong>：使用 github-markdown-css 提供美观的样式
              </li>
              <li>
                <strong>💡 代码语法高亮</strong>：集成 highlight.js，支持多种编程语言
              </li>
              <li>
                <strong>📐 数学公式渲染</strong>：支持 LaTeX 格式的数学公式
              </li>
              <li>
                <strong>📁 文件动态加载</strong>：可以从 URL 加载外部 Markdown 文件
              </li>
              <li>
                <strong>⚡ 实时更新</strong>：支持动态更新内容和实时预览
              </li>
              <li>
                <strong>🔄 平滑过渡</strong>：主题切换时的平滑动画效果
              </li>
              <li>
                <strong>♿ 无障碍访问</strong>：良好的对比度和可访问性
              </li>
            </ul>
          </div>
        </Card>
      </div>
    );
  },
});

export default MarkdownDemo;
