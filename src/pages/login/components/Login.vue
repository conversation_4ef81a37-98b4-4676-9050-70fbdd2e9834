<template>
  <t-form
    ref="form"
    :class="['item-container', `login-${type}`]"
    :data="formData"
    :rules="FORM_RULES"
    label-width="100px"
    label-align="top"
    @submit="onSubmit"
  >
    <t-form-item name="account" label="域账号">
      <t-input v-model="formData.account" size="large" clearable :placeholder="`${t('pages.login.input.account')}`">
        <template #prefix-icon>
          <t-icon name="user" />
        </template>
      </t-input>
    </t-form-item>

    <t-form-item name="password" label="密码">
      <t-input
        v-model="formData.password"
        size="large"
        :type="showPsw ? 'text' : 'password'"
        clearable
        :placeholder="`${t('pages.login.input.password')}`"
      >
        <template #prefix-icon>
          <t-icon name="lock-on" />
        </template>
        <template #suffix-icon>
          <t-icon :name="showPsw ? 'browse' : 'browse-off'" @click="showPsw = !showPsw" />
        </template>
      </t-input>
    </t-form-item>

    <div class="check-container remember-pwd">
      <!-- <t-checkbox v-model="checked">{{ t('pages.login.remember') }}</t-checkbox> -->
      <!-- <span class="tip">{{ t('pages.login.forget') }}</span> -->
    </div>

    <t-form-item class="btn-container">
      <t-button block size="large" type="submit"> {{ t('pages.login.signIn') }} </t-button>
    </t-form-item>
  </t-form>
</template>

<script setup lang="ts">
import type { FormInstanceFunctions, FormRule, SubmitContext } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import { useAppStore, useUserStore } from '@/store';
import { cryptoEncrypt } from '@/utils/secret';
// 初始化国际化方法
const { t } = useI18n(); // 使用 useI18n 获取 t 方法
const store = useUserStore();

const INITIAL_DATA = {
  phone: '',
  account: '',
  password: '',
  verifyCode: '',
  checked: false,
};

const FORM_RULES: Record<string, FormRule[]> = {
  phone: [{ required: true, message: t('pages.login.required.phone'), type: 'error' }],
  account: [{ required: true, message: t('pages.login.required.account'), type: 'error' }],
  password: [{ required: true, message: t('pages.login.required.password'), type: 'error' }],
  verifyCode: [{ required: true, message: t('pages.login.required.verification'), type: 'error' }],
};

const type = ref('password');

const form = ref<FormInstanceFunctions>();
const formData = ref({ ...INITIAL_DATA });
const showPsw = ref(false);
// const checked = ref(false);
const router = useRouter();

const onSubmit = async (ctx: SubmitContext) => {
  if (ctx.validateResult === true) {
    const json = {
      username: formData.value.account,
      password: cryptoEncrypt(formData.value.password, { encodeFormat: 'base64' }),
      clientIp: '127.0.0.1', // 客户端ip地址
    };
    const res = await store.login(json);
    if (!res) return;
    MessagePlugin.success('登录成功');
    const { fullPath } = useAppStore();
    const redirectUrl = fullPath ? decodeURIComponent(fullPath) : '/';
    router.push(redirectUrl);
  }
};
</script>

<style lang="less" scoped>
@import '../index.less';
</style>
