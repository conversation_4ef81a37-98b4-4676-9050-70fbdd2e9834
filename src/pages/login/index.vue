<template>
  <div class="login-wrapper">
    <div class="zh-logo"></div>

    <div class="login-container">
      <div class="absolute">
        <div class="login-title">
          <div class="logo"></div>
          <p>{{ title }}</p>
        </div>
      </div>
      <login />
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'LoginIndex',
};
</script>
<script setup lang="ts">
import Login from './components/Login.vue';

const title = import.meta.env.VITE_APP_TITLE;
</script>

<style lang="less" scoped>
@import './index.less';
</style>
