.light {
  &.login-wrapper {
    background-color: white;
  }
}

.dark {
  &.login-wrapper {
    background-color: var(--td-bg-color-page);
  }
}

.login-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: 100%;
  position: relative;
  background-image: url('@/assets/login-bg.jpg');
  .zh-logo{
    position: absolute;
    left: 120px;
    top: 100px;
    width: 148px;
    height: 32px;
    // background-image: url('@/assets/login-bg.jpg');
    background-size: cover;
  }
}

.login-container {
  position: absolute;
  top: 100px;
  right: 160px;
  width: 504px;
  height: 86vh;
  border-radius: 16px;
  padding: 72px;
  overflow-y: auto;
  background-color: rgba(255,255,255,.6);
  .absolute{
    position: absolute;
    .login-title{
      display: flex;
      align-items: center;
      font-size: 22px;
      font-weight: bold;
      color: #000;
      p {
        margin-left: 4px;
      }
      .logo{
        width: 50px;
        height: 18px;
        display: block;
        background: url('@/assets/logo-hai.svg') no-repeat 0 0;
        background-size: cover;
      }
    }
  }
}
@media (min-width: 768px) {
  .login-container {
    top: 11vh;
    padding-top: 8vh;
    padding-bottom: 8vh;
    height: 86vh;
  }
}
@media (min-width: 1536px) {
  .login-container {
    top: 100px;
    padding-top: 100px;
    height: 700px;
  }
}
.title-container {
  .title {
    font: var(--td-font-headline-large);
    color: var(--td-text-color-primary);
    margin-top: var(--td-comp-margin-xs);

    &.margin-no {
      margin-top: 0;
    }
  }

  .sub-title {
    margin-top: var(--td-comp-margin-xxl);

    .tip {
      display: inline-block;
      margin-right: var(--td-comp-margin-s);
      font: var(--td-font-body-medium);

      &:first-child {
        color: var(--td-text-color-secondary);
      }

      &:last-child {
        color: var(--td-text-color-primary);
        cursor: pointer;
      }
    }
  }
}

.item-container {
  width: 360px;
  position: absolute;
  left: 72px;
  top: 50%;
  transform: translateY(-50%);

  .check-container {
    display: flex;
    align-items: center;

    &.remember-pwd {
      margin-bottom: var(--td-comp-margin-l);
      justify-content: space-between;
      margin-bottom: 80px;
    }

    span {
      color: var(--td-brand-color);

      &:hover {
        cursor: pointer;
      }
    }
  }

  .verification-code {
    display: flex;
    align-items: center;

    :deep(.t-form__controls) {
      width: 100%;

      button {
        flex-shrink: 0;
        margin-left: var(--td-comp-margin-l);
        width: 128px;
      }
    }
  }

  .btn-container {
    margin-top: var(--td-comp-margin-xxxxl);
  }
}

.switch-container {
  margin-top: var(--td-comp-margin-xxl);

  .tip {
    font: var(--td-font-body-medium);
    color: var(--td-brand-color);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-right: var(--td-comp-margin-l);

    &:last-child {
      &::after {
        display: none;
      }
    }

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: var(--td-component-stroke);
      margin-left: var(--td-comp-margin-l);
    }
  }
}

.check-container {
  font: var(--td-font-body-medium);
  color: var(--td-text-color-secondary);
  
  .tip {
    float: right;
    font: var(--td-font-body-medium);
    color: var(--td-brand-color);
  }
}

.copyright {
  font: var(--td-font-body-medium);
  position: absolute;
  left: 5%;
  bottom: 64px;
  color: var(--td-text-color-secondary);
}

@media screen and (height <= 700px) {
  .copyright {
    display: none;
  }
}
