<template>
  <div v-loading="loading" class="api-configuration">
    <t-card class="list-card-container">
      <div class="route-title">
        {{ route.meta.title }}
      </div>
      <t-tabs v-model="tabsValue">
        <!-- 默认插槽 和 具名插槽（panel）都是用来渲染面板内容 -->
        <t-tab-panel value="first" label="API配置" :destroy-on-hide="false">
          <div class="table-header">
            <t-button v-permission="menuBlockList.includes('ADD')" theme="primary" @click="onCreateApi">新增</t-button>
          </div>

          <t-table
            :data="tableData"
            :columns="columns"
            :pagination="pagination"
            row-key="id"
            @page-change="onPageChange"
          >
            <template #op="{ row }">
              <t-space>
                <t-link theme="primary" hover="color" @click="showApiKey(row)">查看</t-link>
                <t-popconfirm theme="danger" :content="`确定要刷新 ${row.name} 吗？`" @confirm="onRefresh(row)">
                  <t-link v-permission="menuBlockList.includes('REFRESH')" theme="primary" hover="color">刷新</t-link>
                </t-popconfirm>
                <t-popconfirm theme="danger" :content="`确定要删除 ${row.name} 吗？`" @confirm="onDelete(row)">
                  <t-link v-permission="menuBlockList.includes('DELETE')" theme="danger" hover="color">删除</t-link>
                </t-popconfirm>
              </t-space>
            </template>
          </t-table>
        </t-tab-panel>
        <t-tab-panel value="second" label="API文档" :destroy-on-hide="false">
          <markdown-component file-path="/template/haicont.md" theme="auto" />
        </t-tab-panel>
      </t-tabs>
    </t-card>

    <t-dialog
      v-model:visible="dialogVisible"
      header="密钥名称"
      :confirm-btn="{ content: '创建', theme: 'primary', disabled: !formData.name, loading: loading2 }"
      :cancel-btn="{ content: '取消' }"
      @confirm="onDialogConfirm"
    >
      <t-form ref="form" style="height: 60px" :rules="rules" :data="formData">
        <t-form-item label="API 名称" name="name">
          <t-input v-model="formData.name" maxlength="30" show-limit-number placeholder="请输入API 名称" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- API Key 对话框 -->
    <t-dialog
      v-model:visible="apiKeyDialog.visible"
      header="API密钥"
      :confirm-btn="{ content: '好的', theme: 'primary' }"
      :cancel-btn="null"
      @confirm="hiddenApiKey"
    >
      <div class="api-key-content">
        <p class="api-key-tip">请将此密钥保存在安全且可访问的地方。</p>
        <div class="api-key-body">
          <div class="api-key-value">{{ apiKeyDialog.apiKey }}</div>
          <div class="api-key-h">|</div>
          <div class="api-key-copy" @click="copyApiKey"><task1-icon /></div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ApiConfiguration',
};
</script>
<script lang="ts" setup>
import { useClipboard } from '@vueuse/core';
import { Task1Icon } from 'tdesign-icons-vue-next';
import type { FormRule, PageInfo, PrimaryTableCol } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { onActivated, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { deleteById, getById, pageByVo, save, updateById } from '@/api/modules/api';
import MarkdownComponent from '@/components/markdown';
// 获取当前路由信息
const route = useRoute();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

interface ApiItem {
  name: string;
  [key: string]: any;
}

interface PaginationState {
  total: number;
  size: number;
  current: number;
}
const tabsValue = ref('first'); //  当前激活的标签页
const loading = ref(false); // table加载状态
const loading2 = ref(false); // form加载状态
const tableData = ref<ApiItem[]>([]); // 表格数据
const dialogVisible = ref(false); // dialog显示状态
const isEdit = ref(false); //  当前操作类型，true为编辑，false为创建
const pagination = reactive<PaginationState>({
  total: 0,
  size: 10,
  current: 1,
}); // 分页信息

const formData = reactive<ApiItem>({
  name: '',
}); //  表单数据

const rules: Record<string, FormRule[]> = {
  name: [{ required: true, message: '请输入API 名称', type: 'error' }],
  apiKey: [{ required: true, message: '请输入apiKey', type: 'error' }],
}; //  表单验证规则

const columns: PrimaryTableCol[] = [
  { colKey: 'name', title: '名称', width: 300, ellipsis: true, fixed: 'left' },
  { colKey: 'apiKey', title: 'API KEY', width: 300, ellipsis: true },
  { colKey: 'createdTime', title: '创建时间', width: 160 },
  { colKey: 'op', title: '操作', width: 100, fixed: 'right' },
];
// 获取表格数据
const onSearch = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    await fetchTableData();
  } finally {
    loading.value = false;
  }
};
// 分页切换
const onPageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  if (pageInfo.pageSize) pagination.size = pageInfo.pageSize;
  onSearch();
};

const form = ref(); // 表单实例
// 创建/编辑表单提交
const onDialogConfirm = async () => {
  if (loading2.value) return;

  const validateResult = await form.value.validate();
  if (validateResult === true) {
    loading2.value = true;
    try {
      let res: any;
      // 提取操作类型
      const action = isEdit.value ? '编辑' : '创建';
      // 深拷贝 formData 避免副作用
      const submitData = JSON.parse(JSON.stringify(formData));

      if (isEdit.value) {
        res = await updateById(submitData);
      } else {
        delete submitData.id;
        res = await save(submitData);
      }

      if (!res) {
        MessagePlugin.error(`${action}失败`);
        return;
      }

      MessagePlugin.success(`${action}成功`);
      dialogVisible.value = false;
      isEdit.value = false;
      onSearch();

      // 显示 API Key 对话框
      apiKeyDialog.visible = true;
      apiKeyDialog.apiKey = res;
    } finally {
      loading2.value = false;
    }
  }
};
// 创建 API Key
const onCreateApi = () => {
  isEdit.value = false;
  formData.id = null;
  formData.name = '';
  dialogVisible.value = true;
  form.value?.clearValidate();
};
// const editItem = (row: ApiItem) => {
//   isEdit.value = true;
//   formData.id = row.id;
//   formData.name = row.name;
//   dialogVisible.value = true;
//   form.value?.clearValidate();
// };
// 刷新 API Key
const onRefresh = (row: any) => {
  MessagePlugin.success('刷新成功');
};
// 删除 API Key
const onDelete = (row: ApiItem) => {
  deleteById(row.id).then((res) => {
    if (!res) {
      MessagePlugin.error('删除失败');
      return;
    }
    MessagePlugin.success('删除成功');
    onSearch();
  });
};

// 获取列表
const fetchTableData = async () => {
  try {
    const { current, size } = pagination;
    const res = await pageByVo({ current, size });
    tableData.value = res.records || [];
    pagination.total = Number(res.total) || 0;
  } catch (error) {
    console.error('获取列表失败:', error);
  }
};

// 添加 API Key 对话框状态
const apiKeyDialog = reactive({
  visible: false,
  apiKey: '', // 这里应该从后端获取
});

// 显示 API Key 对话框
const showApiKey = (row: any) => {
  getById(row.id).then((res) => {
    apiKeyDialog.visible = true;
    apiKeyDialog.apiKey = res.apiKey;
  });
};
// 隐藏 API Key 对话框
const hiddenApiKey = () => {
  apiKeyDialog.visible = false;
  apiKeyDialog.apiKey = '';
};

// 复制 API Key
const { copy } = useClipboard();
const copyApiKey = async () => {
  await copy(apiKeyDialog.apiKey);
  MessagePlugin.success('API密钥已复制到剪贴板');
};

onMounted(() => {
  onSearch();
});
onActivated(() => {
  onSearch();
});
</script>

<style lang="less" scoped>
.api-configuration {
  padding: 0;
  .list-card-container {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);

    :deep(.t-card__body) {
      padding: 0;
    }
  }
  .route-title {
    height: 22px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
}
.table-header {
  margin: 10px 0;
  text-align: right;
}
.api-key-content {
  .api-key-tip {
    color: var(--td-gray-color-6);
    margin-bottom: 16px;
  }
  .api-key-body {
    padding: 0 10px;
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 4px;
    background-color: #e7e8f0;
    .api-key-value {
      flex: 1;
      width: 0;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .api-key-h {
      min-width: 20px;
      text-align: center;
    }
    .api-key-copy {
      cursor: pointer;
      font-size: 16px;
    }
  }
}
</style>
