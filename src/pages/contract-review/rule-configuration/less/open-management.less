.open-management::v-deep{
  padding: 20px 10px;
  display: block !important;
  .form-container{
    width: 850px;
  }
  .rule-name{
    font-weight: bold;
    margin-bottom: 20px;
  }
  .form-title {
    height: 22px;
    font-size: 12px;
    color: var(--td-brand-color-active);
    font-weight: bold;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }

  .rules-grid {
    padding-left: 10px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }
  .selection-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    background-color: #eee;
    border-radius: 4px;
    padding: 10px;

    .count {
      color: var(--el-text-color-secondary);
      margin-left: 20px;
    }
  }
  .t-is-disabled + .count {
    color: var(--td-text-color-disabled);
  }

  .radio-group{
    flex-direction: column;
    align-items: flex-start;
  }

  .tenant-name{
    margin-top: 10px;
    line-height: 24px;
    &.disabled{
      color: var(--td-text-color-disabled);
    }
  }
  label::before {
    content: '';
    display: none;
  }

  .btn-container{
    margin-top: 50px;
    padding-left: 100px;
    .t-button{
      width: 100px;
    }
  }
}