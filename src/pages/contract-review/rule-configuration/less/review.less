.resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 10px;
  cursor: ew-resize;
  z-index: 1000;
  display: none;
  .handle {
    position: absolute;
    right: -2px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    background: #dfe4e8;
  }
}
.new-rule-page {
  height: 100%;

  .list-card-container {
    height: 100%;
    padding: var(--td-comp-paddingLR-xl);
    padding-top: 10px;
    padding-bottom: 0;

    :deep(.t-card__body) {
      padding: 0;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
  .route-title {
    height: 40px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }

  .col-title,
  .col-title-text {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -24px;
      width: calc(100% + 48px);
      height: 1px;
      background-color: #ebebeb;
    }
    .col-title-text-title {
      display: flex;
      align-items: center;
      color: var(--td-brand-color);
      cursor: pointer;
      .t-icon {
        font-size: 20px;
      }
    }
    .col-title-text-subtitle {
      margin-right: 20px;
      font-size: 14px;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: calc(100% - 240px);
      color: var(--td-brand-color);
    }
  }

  .page-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    height: calc(100% - 40px);

    .review-list {
      min-width: 260px;
      border-right: 1px solid #e4e7ed;
      display: flex;
      flex-direction: column;
      position: relative;
      &:hover {
        .resize-handle {
          display: block;
        }
      }

      .list-header {
        padding: 12px;
        border-bottom: 1px solid #e4e7ed;

        .list-name {
          font-weight: bold;
          margin-bottom: 8px;
        }
        .list-action {
          display: flex;
          align-items: center;
          padding-left: 12px;

          span {
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .optimize-icon {
            margin-left: 4px;
            min-width: 20px;
            color: var(--td-brand-color);
            cursor: pointer;
          }
        }
      }
      .list-nav {
        padding: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .nav-name {
          font-weight: bold;
        }
        .nav-button {
          cursor: pointer;
          margin: 4px 0;
          padding: 5px 10px;
          border-radius: 4px;
          &:hover {
            color: var(--td-brand-color);
            background-color: var(--td-brand-color-light);
          }
        }
      }

      .list-content {
        overflow: hidden;
        overflow-y: auto;
        padding: 12px;
        height: calc(100vh - 275px);

        .review-item {
          height: 36px;
          line-height: 36px;
          padding: 0 12px;
          cursor: pointer;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .review-item-title{
            flex: 1;
            width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .review-item-icon {
            display: none;
            margin-left: 4px;
            min-width: 20px;
            color: var(--td-brand-color);
            cursor: pointer;
            &.delete-icon {
              color: var(--td-error-color-6);
            }
          }

          &:hover {
            .review-item-icon {
              display: block;
            }
          }

          &.active {
            color: var(--td-brand-color);
            background-color: var(--td-brand-color-light);
          }
        }
      }
    }

    .review-detail {
      flex: 1;
      width: 0;
      padding: 20px;
      padding-right: 0;

      .review-detail-form {
        width: 100%;
        height: 100%;
        .review-detail-form-header{
          display: flex;
          margin: -8px 0 8px -20px;
          padding-left: 20px;
          padding-bottom: 8px;
          font-weight: bold;
          border-bottom: 1px solid #e4e7ed;
          .btn {
            margin-left: auto;
          }
        }
        .review-detail-form-tips {
          line-height: 40px;
          margin-bottom: 8px;
          text-align: center;
          background-color: #f2f2f2;
          border-radius: 6px;
          color: rgba(0, 0, 0, 0.8);
        }
        .detail-footer {
          padding-top: 10px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--td-bg-color-container);
        }
        .detail-content {
          height: calc(100vh - 280px);
          overflow: hidden;
          overflow-y: auto;
        }
      }

      .section-content {
        position: relative;

        .optimize-btn {
          position: absolute;
          left: 10px;
          bottom: 25px;
        }
        :deep(.t-textarea__inner) {
          padding-bottom: 34px;
        }
      }
    }
  }
}
:deep(.review-detail .t-form__label label) {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.optimize-icon{
  font-size: 14px;
  color: var(--td-brand-color);
  margin-left: 6px;
}