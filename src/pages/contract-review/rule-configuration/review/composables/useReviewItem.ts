// src/pages/contract-review/rule-configuration/composables/useReviewItem.ts
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

import { reviewRuleGetById, reviewRuleUpdateById } from '@/api/modules/rule';
import type { RuleVo } from '@/types/review';

export function useReviewItem() {
  const activeReviewItem = ref<RuleVo | null>(null); // 当前操作的规则项详情
  const loading = ref(false); // 加载状态，防止重复请求或用于 UI 控制

  // 获取规则详情
  const getReviewItemDetail = async (id: string) => {
    loading.value = true; // 开启加载状态
    reviewRuleGetById(id)
      .then((data: any) => {
        activeReviewItem.value = data; // 设置当前规则项数据
      })
      .finally(() => {
        loading.value = false; // 关闭加载状态
      });
  };

  // 更新规则项
  const updateReviewItem = async (params: any) => {
    loading.value = true; // 开启加载状态
    try {
      const res = await reviewRuleUpdateById(params); // 调用更新接口
      if (res) {
        MessagePlugin.success('更新成功'); // 成功提示
      } else {
        MessagePlugin.error('更新失败'); // 失败提示
      }
    } finally {
      loading.value = false; // 确保无论成功与否都关闭加载状态
    }
  };

  return {
    activeReviewItem,
    loading,
    getReviewItemDetail,
    updateReviewItem,
  };
}
