import { onMounted, onUnmounted, ref } from 'vue';

export default function useResize(id: string, minW: number = 260, maxW: number = 700) {
  const resizableWidth = ref(minW);
  const isResizing = ref(false);
  const clientX = ref(0);
  function startResize() {
    clientX.value = document.getElementById(id)?.getBoundingClientRect()?.left || 0;
    isResizing.value = true;
  }
  function resize(event: { clientX: number }) {
    if (!isResizing.value) return;

    requestAnimationFrame(() => {
      const maxWidth = Math.max(event.clientX - clientX.value, minW);
      const minWidth = Math.min(maxWidth, maxW);

      resizableWidth.value = minWidth;
    });
  }
  function stopResize() {
    isResizing.value = false;
  }

  onMounted(() => {
    window.addEventListener('mouseup', stopResize);
    window.addEventListener('mousemove', resize);
  });
  onUnmounted(() => {
    window.removeEventListener('mouseup', stopResize);
    window.removeEventListener('mousemove', resize);
  });

  return {
    resizableWidth,
    isResizing,
    startResize,
  };
}
