// src/pages/contract-review/rule-configuration/composables/useOptimize.ts
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

export function useOptimize() {
  const isOptimizing = ref(false); // 控制优化按钮加载状态或防止重复触发

  // 触发优化流程（前置校验
  const handleOptimizeRule = (activeReviewItem: any) => {
    if (!activeReviewItem?.rule) {
      MessagePlugin.warning('请先输入审查规则'); // 提示用户输入规则内容
      return;
    }
    isOptimizing.value = true; // 开启优化流程标志
  };

  // 执行优化操作
  const handleOptimize = (activeReviewItem: any, optimizedRule: string) => {
    if (!activeReviewItem) return;

    try {
      activeReviewItem.rule = optimizedRule; // 更新为优化后的规则内容
      MessagePlugin.success('优化成功');
    } catch (error) {
      console.error('优化规则失败:', error); // 输出错误信息便于排查
      MessagePlugin.error('优化失败');
    } finally {
      isOptimizing.value = false; // 关闭优化状态
    }
  };

  return {
    isOptimizing,
    handleOptimizeRule,
    handleOptimize,
  };
}
