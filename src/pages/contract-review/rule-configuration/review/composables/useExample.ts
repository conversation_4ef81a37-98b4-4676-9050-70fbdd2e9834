// src/pages/contract-review/rule-configuration/composables/useExample.ts
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

import type { ExampleList } from '@/types/review';

export function useExample() {
  const exampleIsEdit = ref(false); // 是否处于编辑状态
  const exampleDialogVisible = ref(false); // 控制弹窗是否可见
  const activeExampleItem = ref<ExampleList | null>(null); // 当前操作的示例项

  // 添加示例
  const handleAddExample = () => {
    exampleDialogVisible.value = true; // 显示新增弹窗
  };

  // 编辑示例
  const handleEditExample = (row: ExampleList) => {
    if (!row) return;
    activeExampleItem.value = row; // 设置当前编辑项
    exampleDialogVisible.value = true; // 显示弹窗
    exampleIsEdit.value = true; // 设置为编辑模式
  };

  // 删除示例
  const handleDeleteExample = (activeReviewItem: any, row: ExampleList) => {
    if (!activeReviewItem) return;

    // 查找要删除的索引（根据 id 或 newId）
    const index = activeReviewItem.exampleList.findIndex(
      (e: any) => (e.newId && e.newId === row.newId) || e.id === row.id,
    );

    if (index > -1) {
      activeReviewItem.exampleList.splice(index, 1); // 删除该条数据
    }
  };

  // 提交示例（新增或更新）
  const handleConfirmExample = (activeReviewItem: any, data: ExampleList) => {
    if (!activeReviewItem) return;

    if (exampleIsEdit.value) {
      // 编辑逻辑
      const activeItem = activeReviewItem.exampleList.find(
        (item: any) =>
          item.id === activeExampleItem.value.id || (item.newId && item.newId === activeExampleItem.value.newId),
      );

      if (activeItem) {
        // 更新已有项的字段
        activeItem.contractContent = data.contractContent;
        activeItem.correctResult = data.correctResult;
        activeItem.reviewDescription = data.reviewDescription;
      } else {
        MessagePlugin.error('编辑失败'); // 找不到对应项时提示错误
      }
    } else {
      // 新增逻辑
      const newExample: ExampleList = {
        newId: Date.now().toString(), // 生成临时唯一 ID
        contractContent: data.contractContent,
        ruleOrigin: data.ruleOrigin,
        correctResult: data.correctResult,
        reviewDescription: data.reviewDescription,
      };

      activeReviewItem.exampleList.unshift(newExample); // 插入到数组开头
    }

    // 重置状态
    exampleIsEdit.value = false;
    exampleDialogVisible.value = false;
    activeExampleItem.value = null;
  };

  // 将状态和方法暴露给组件使用。
  return {
    exampleIsEdit,
    exampleDialogVisible,
    activeExampleItem,
    handleAddExample,
    handleEditExample,
    handleDeleteExample,
    handleConfirmExample,
  };
}
