<template>
  <div v-loading="loading" class="new-rule-page" :style="{ userSelect: isResizing ? 'none' : 'auto' }">
    <t-card class="list-card-container">
      <div class="col-title">
        <div class="col-title-text">
          <div class="col-title-text-title" @click="handleBack">
            <chevron-left-s-icon />
            返回&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;
          </div>
          <div class="col-title-text-subtitle">{{ route.meta.title }}</div>
        </div>
      </div>

      <div class="page-content">
        <!-- 左侧审查清单 -->
        <div id="ew-resize" class="review-list" :style="{ width: `${resizableWidth}px` }">
          <div class="list-header">
            <div class="list-name">审查清单名称</div>
            <div class="list-action" :title="currentRuleDetail.name">
              <span>{{ currentRuleDetail.name }}</span>
              <edit2-icon
                v-if="currentRuleDetail.sourceType !== 'SYSTEM' || isAdmin"
                v-permission="menuBlockList.includes('EDIT_RULE_NAME')"
                class="optimize-icon"
                @click="onEdit"
              />
            </div>
          </div>

          <div class="list-nav">
            <div class="nav-name">审查项</div>
            <t-popup placement="bottom-left" attach="list-nav">
              <t-button
                v-if="currentRuleDetail.sourceType !== 'SYSTEM' || isAdmin"
                v-permission="
                  menuBlockList.includes('RULE_ADD') ||
                  menuBlockList.includes('IMPORT_ADD') ||
                  menuBlockList.includes('TEMPLATE_IMPORT_ADD')
                "
                theme="primary"
                variant="outline"
              >
                <template #icon><add-icon /></template>新增项
              </t-button>
              <template #content>
                <div v-permission="menuBlockList.includes('RULE_ADD')" class="nav-button" @click="handleAdd">
                  手动新增
                </div>
                <div v-permission="menuBlockList.includes('IMPORT_ADD')" class="nav-button" @click="handleImportRule">
                  从其他清单导入
                </div>
                <div
                  v-permission="menuBlockList.includes('TEMPLATE_IMPORT_ADD')"
                  class="nav-button"
                  @click="handleImportTemplateRule"
                >
                  使用模板批量导入
                </div>
              </template>
            </t-popup>
          </div>

          <div class="list-content">
            <template v-if="(currentRuleDetail.ruleVos || []).length">
              <div
                v-for="item in currentRuleDetail.ruleVos"
                :key="item.id"
                class="review-item"
                :class="{ active: activeReviewItem?.id === item.id }"
                @click="getReviewItemDetail(item.id)"
              >
                <span class="review-item-title">
                  <t-tooltip :content="item.name" theme="primary">
                    {{ item.name }}
                  </t-tooltip>
                </span>

                <!-- 编辑审查项 -->
                <div
                  v-if="(currentRuleDetail.sourceType !== 'SYSTEM' && item.ruleSource === 'MANUAL') || isAdmin"
                  style="display: flex"
                >
                  <span @click.stop="handleEdit(item)">
                    <edit2-icon class="review-item-icon" />
                  </span>
                  <span @click.stop="handleDelete(item)">
                    <delete1-icon class="review-item-icon delete-icon" />
                  </span>
                </div>
              </div>
            </template>
            <t-empty v-else style="margin-top: 100px" />
          </div>
          <div class="resize-handle" @mousedown="startResize">
            <!-- 拖拽手柄 -->
            <div class="handle"></div>
          </div>
        </div>

        <!-- 右侧详情 -->
        <div class="review-detail">
          <template v-if="activeReviewItem">
            <t-form
              ref="ruleForm"
              :key="activeReviewItem.isEdit ? 'edit' : 'add'"
              class="review-detail-form"
              :data="activeReviewItem"
              :rules="activeReviewItem.isEdit ? rules : {}"
              label-align="top"
              @submit="handleUpdateReviewItem"
            >
              <div class="review-detail-form-header">
                审查项详情
                <t-button
                  v-if="
                    (currentRuleDetail.sourceType !== 'SYSTEM' && !activeReviewItem.isEdit && isManual) ||
                    (!activeReviewItem.isEdit && isAdmin)
                  "
                  v-permission="menuBlockList.includes('EDIT_RULE')"
                  class="btn"
                  theme="primary"
                  @click="handleEditReviewItem"
                >
                  编辑规则
                </t-button>
              </div>
              <div v-if="!isManual" class="review-detail-form-tips">
                预置清单不支持编辑，您可以将当前规则导入其他自建清单后再进行编辑。
              </div>
              <div class="detail-content">
                <t-form-item class="section-content" name="rule">
                  <template #label>
                    <t-space>
                      <div class="section-title">审查规则</div>
                      <t-tooltip theme="primary" :duration="3000">
                        <template #content>
                          审查项（示例）：合同总金额大小写一致 <br />
                          审查规则（示例）： <br />
                          1、合同总金额，指合同含税总价、不含税总价<br />
                          2、合同总金额通常在合同协议书-工程概况、工程价款的条款中进行约定<br />
                          3、请审查合同含税总价的大小写是否一致、不含税总价的大小写是否一致<br />
                        </template>
                        <!-- <t-button v-if="activeReviewItem.isEdit" variant="outline" theme="primary" size="small">
                          填写示例
                        </t-button> -->
                        <t-tag v-if="activeReviewItem.isEdit" variant="outline" theme="primary" size="small">
                          填写示例
                        </t-tag>
                      </t-tooltip>
                    </t-space>
                  </template>
                  <template v-if="activeReviewItem.isEdit">
                    <t-textarea
                      v-model="activeReviewItem.rule"
                      :autosize="{ minRows: 4 }"
                      placeholder="请输入审查规则"
                      maxlength="1000"
                      autofocus
                    />
                    <t-button
                      variant="outline"
                      class="optimize-btn"
                      size="small"
                      theme="primary"
                      :loading="isOptimizing"
                      @click="handleOptimizeRule(activeReviewItem)"
                    >
                      智能优化
                    </t-button>
                  </template>
                  <template v-else>
                    {{ activeReviewItem.rule || '-' }}
                  </template>
                </t-form-item>
                <t-form-item name="riskLevel">
                  <template #label>
                    风险级别
                    <t-tooltip
                      theme="primary"
                      :duration="3000"
                      content="指该类风险在业务上的重要程度，不影响AI审查，仅便于您对审查结果进行分类"
                    >
                      <info-circle-icon class="optimize-icon" />
                    </t-tooltip>
                  </template>

                  <template v-if="activeReviewItem.isEdit">
                    <t-select v-model="activeReviewItem.riskLevel" placeholder="请选择风险级别" style="width: 300px">
                      <t-option
                        v-for="option in riskLevelOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </t-select>
                  </template>
                  <template v-else>
                    {{ getRiskLevelLabel(activeReviewItem.riskLevel) }}
                  </template>
                </t-form-item>
                <t-form-item v-if="isManual">
                  <template #label>
                    审查示例
                    <t-tooltip
                      theme="primary"
                      :duration="3000"
                      content="提供基于真实合同的审查示例，并对审查逻辑进行详细说明，AI将学习示例，显著提升同类规则的审查效果"
                    >
                      <info-circle-icon class="optimize-icon" />
                    </t-tooltip>
                    <t-button
                      v-if="activeReviewItem.isEdit"
                      variant="outline"
                      theme="primary"
                      size="small"
                      style="margin-left: 10px"
                      @click.stop="handleAddExample"
                    >
                      <template #icon>
                        <add-icon />
                      </template>
                      新增示例
                    </t-button>
                  </template>
                  <t-table
                    v-if="activeReviewItem?.exampleList?.length"
                    :data="activeReviewItem.exampleList"
                    :columns="columnsComputed"
                    row-key="id"
                    table-layout="fixed"
                  >
                    <template #correctResult="{ row }">
                      {{ row.correctResult === 'NONE' ? '无风险' : '存在风险' }}
                    </template>
                    <template #op="{ row }">
                      <t-space v-if="activeReviewItem.isEdit">
                        <t-link theme="primary" hover="color" @click="handleEditExample(row)">编辑</t-link>
                        <t-popconfirm
                          theme="danger"
                          :content="`确定要删除示例吗？`"
                          @confirm="handleDeleteExample(activeReviewItem, row)"
                        >
                          <t-link theme="danger" hover="color">删除</t-link>
                        </t-popconfirm>
                      </t-space>
                    </template>
                  </t-table>
                </t-form-item>
              </div>
              <div v-if="isManual" class="detail-footer">
                <t-form-item v-if="activeReviewItem.isEdit">
                  <div style="text-align: center; width: 100%">
                    <t-button theme="primary" type="submit"> 保存 </t-button>
                    <t-button theme="default" variant="base" @click="handleCancelEditReviewItem"> 取消 </t-button>
                  </div>
                </t-form-item>
              </div>
            </t-form>
          </template>
          <t-empty v-else style="margin-top: 224px" />
        </div>
      </div>
    </t-card>
    <!-- 新增审查项弹窗 -->
    <add-review-item-dialog
      v-if="showAddDialog"
      ref="addReviewItemDialog"
      v-model:visible="showAddDialog"
      :name="editReviewItem?.name"
      :title="showAddDialogTitle"
      @confirm="handleAddItem"
    />
    <!-- 导入规则弹窗 -->
    <import-rule-dialog
      v-model:visible="showImportRuleDialog"
      :current-rule-detail="currentRuleDetail"
      @confirm="handleImportRuleItem"
    />
    <import-template-dialog
      v-if="showImportTemplateDialog"
      v-model:visible="showImportTemplateDialog"
      :current-rule-detail="currentRuleDetail"
      @confirm="() => getRuleListDetail(route.params.id as string)"
    ></import-template-dialog>
    <!-- 优化规则弹窗 -->
    <optimize-rule-dialog
      v-if="activeReviewItem && isOptimizing"
      v-model:visible="isOptimizing"
      :initial-data="activeReviewItem"
      @confirm="(data) => handleOptimize(activeReviewItem, data)"
    />
    <!-- 清单名称弹窗 -->
    <rule-form-dialog
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :is-edit="true"
      :initial-data="currentRuleDetail"
      @confirm="onDialogConfirm"
    />
    <!-- 示例 -->
    <example-form-dialog
      v-if="exampleDialogVisible"
      v-model:visible="exampleDialogVisible"
      :is-edit="exampleIsEdit"
      :name="activeReviewItem?.name"
      :initial-data="activeExampleItem"
      @confirm="(data) => handleConfirmExample(activeReviewItem, data)"
    />
    <t-dialog
      v-if="ruleItemVisible"
      v-model:visible="ruleItemVisible"
      theme="info"
      header="删除审查项"
      :body="`确定要删除规则清单 ${editReviewItem?.name} 吗？`"
      :on-close="onClose"
      @confirm="onConfirm"
    />
  </div>
</template>
<script setup lang="ts">
import { AddIcon, ChevronLeftSIcon, Delete1Icon, Edit2Icon, InfoCircleIcon } from 'tdesign-icons-vue-next';
import type { FormRule } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  getById,
  reviewRuleDeleteById,
  reviewRuleSave,
  reviewRuleUpdateById,
  saveBatch,
  updateById,
} from '@/api/modules/rule';
import type { SaveRequest, UpdateByIdRequest } from '@/api/types/rule';
import type { RuleVoDetail } from '@/types/review';
// 获取当前路由信息
const route = useRoute();
const router = useRouter();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

import { useUserStore } from '@/store';

const {
  userInfo: { ownerStatus, adminStatus },
} = useUserStore();
const isAdmin = computed(() => {
  return ownerStatus === 1 || adminStatus === 1;
});
import AddReviewItemDialog from '../components/AddReviewItemDialog.vue';
import ExampleFormDialog from '../components/ExampleFormDialog.vue';
import ImportRuleDialog from '../components/ImportRuleDialog.vue';
import ImportTemplateDialog from '../components/ImportTemplateDialog.vue';
import OptimizeRuleDialog from '../components/OptimizeRuleDialog.vue';
import RuleFormDialog from '../components/RuleFormDialog.vue';
import { useExample } from './composables/useExample';
import { useOptimize } from './composables/useOptimize';
import useResize from './composables/useResize';
import { useReviewItem } from './composables/useReviewItem';
import { RISK_LEVEL_OPTIONS, TABLE_COLUMNS } from './constants';
// 使用组合式函数
const { activeReviewItem, loading, getReviewItemDetail } = useReviewItem();

const {
  exampleIsEdit,
  exampleDialogVisible,
  activeExampleItem,
  handleAddExample,
  handleEditExample,
  handleDeleteExample,
  handleConfirmExample,
} = useExample();

const { isOptimizing, handleOptimizeRule, handleOptimize } = useOptimize();
const { resizableWidth, isResizing, startResize } = useResize('ew-resize');

const ruleForm = ref();
const rules: Record<string, FormRule[]> = {
  rule: [{ required: true, message: '请输入审查规则', type: 'error' }],
  riskLevel: [{ required: true, message: '请选择风险级别', type: 'error' }],
};
const showAddDialog = ref(false); // 控制新增审查项弹窗的显示状态
const showAddDialogTitle = ref('新增审查项'); // 新增/编辑审查项弹窗标题
const showImportRuleDialog = ref(false); // 控制从其他清单导入规则弹窗的显示状态
const ruleItemVisible = ref(false); // 用于控制删除规则项相关弹窗的显示
const showImportTemplateDialog = ref(false); // 控制使用模板批量导入规则弹窗的显示状态

/** 当前清单的详细信息 */
const currentRuleDetail = ref<RuleVoDetail>({
  id: '',
  name: '',
  description: '',
  ruleVos: [],
});

/** 风险等级选项 */
const riskLevelOptions = RISK_LEVEL_OPTIONS;
// 获取风险等级标签
const getRiskLevelLabel = (value: string) => {
  const item = riskLevelOptions.find((item) => item.value === value);
  return item?.label || value;
};
const columns: any[] = TABLE_COLUMNS;

const columnsComputed = computed(() => {
  if (activeReviewItem.value?.isEdit) {
    return columns;
  }
  return columns.filter((item) => item.colKey !== 'op');
});
// 规则来源 LLM-大语言模型生成、MANUAL-人工编写
const isManual = computed(() => {
  return activeReviewItem.value?.ruleSource === 'MANUAL';
});
/** 编辑规则清单名称状态 */
const dialogVisible = ref(false);
/** 编辑规则清单名称按钮事件 */
const onEdit = () => {
  dialogVisible.value = true;
};
/** 编辑规则清单名称保存事件 */
const onDialogConfirm = async (data: SaveRequest) => {
  try {
    const updateParams: UpdateByIdRequest = {
      id: currentRuleDetail.value.id,
      name: data.name,
      description: data.description,
    };
    updateById(updateParams).then((res) => {
      if (!res) {
        MessagePlugin.error('编辑失败');
        return;
      }

      MessagePlugin.success('编辑成功');
      dialogVisible.value = false;
      getRuleListDetail(currentRuleDetail.value.id);
    });
  } catch (error) {
    console.log(error);
  }
};

/** 获取规则清单详情 */
const getRuleListDetail = async (id: string) => {
  loading.value = true;
  getById(id)
    .then((data: any) => {
      currentRuleDetail.value = data;
      if (data.ruleVos?.length && activeReviewItem.value?.id) {
        getReviewItemDetail(activeReviewItem.value?.id);
      } else if (data.ruleVos?.length) {
        getReviewItemDetail(data.ruleVos[0].id);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 手动新增
const handleAdd = () => {
  editReviewItem.value = null;
  showAddDialog.value = true;
  showAddDialogTitle.value = '新增审查项';
};
const editReviewItem = ref(null);
const handleEdit = (row: any) => {
  editReviewItem.value = row;
  showAddDialog.value = true;
  showAddDialogTitle.value = '编辑审查项';
};
const handleDelete = (row: any) => {
  editReviewItem.value = row;
  ruleItemVisible.value = true;
};
const onConfirm = () => {
  if (!editReviewItem.value) return;
  loading.value = true;
  const { id } = editReviewItem.value;

  reviewRuleDeleteById(id)
    .then((res: any) => {
      if (res) {
        MessagePlugin.success('删除成功');
        if (activeReviewItem.value.id === id) {
          activeReviewItem.value = null;
          getRuleListDetail(currentRuleDetail.value.id);
        } else {
          const { ruleVos } = currentRuleDetail.value;
          const index = ruleVos.findIndex((item: any) => item.id === id);
          if (index > -1) ruleVos.splice(index, 1);
        }
        onClose();
      } else {
        MessagePlugin.error('删除失败');
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const onClose = () => {
  ruleItemVisible.value = false;
  editReviewItem.value = null;
};
// 新增审查项
const handleAddItem = async (name: string) => {
  if (!editReviewItem.value) {
    const params: any = {
      name,
      ruleListId: currentRuleDetail.value.id,
      ruleSource: 'MANUAL',
    };
    reviewRuleSave(params).then(() => {
      MessagePlugin.success('新增成功');
      showAddDialog.value = false;
      getRuleListDetail(currentRuleDetail.value.id);
    });
  } else {
    const params: any = {
      ...editReviewItem.value,
      name,
    };
    reviewRuleUpdateById(params).then(() => {
      MessagePlugin.success('编辑成功');
      showAddDialog.value = false;
      const item = currentRuleDetail.value.ruleVos?.find((item) => item.id === editReviewItem.value.id);
      if (item) {
        item.name = name;
      }
    });
  }
};
// 更新审查项
const handleUpdateReviewItem = async ({ validateResult, firstError }: any) => {
  if (!activeReviewItem.value) return;
  if (validateResult === true) {
    const exampleList = activeReviewItem.value.exampleList?.map((item: any) => {
      return {
        ...item,
        reviewRuleId: activeReviewItem.value.id,
        ruleOrigin: item.ruleOrigin || 'FEEDBACK',
      };
    });
    const params = {
      id: activeReviewItem.value.id,
      name: activeReviewItem.value.name,
      rule: activeReviewItem.value.rule,
      riskLevel: activeReviewItem.value.riskLevel,
      ruleListId: currentRuleDetail.value.id,
      code: activeReviewItem.value.code || '',
      relationId: activeReviewItem.value.relationId || '',
      ruleSource: activeReviewItem.value.ruleSource || 'MANUAL',
      tenantId: activeReviewItem.value.tenantId || '',
      userId: activeReviewItem.value.userId || '',
      exampleList: exampleList || [],
    };
    loading.value = true;
    reviewRuleUpdateById(params)
      .then((res: any) => {
        if (res) {
          MessagePlugin.success('更新成功');
          handleCancelEditReviewItem();
        } else {
          MessagePlugin.error('更新失败');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    MessagePlugin.warning(firstError);
  }
};

// 从其他清单导入
const handleImportRule = () => {
  showImportRuleDialog.value = true;
};
const handleImportTemplateRule = () => {
  showImportTemplateDialog.value = true;
};

// 导入审查项
const handleImportRuleItem = async (data: { id: string; value: string[] }) => {
  saveBatch(currentRuleDetail.value.id, data.value).then(async (res: any) => {
    if (res) {
      const id = route.params.id as string;
      if (id) {
        await getRuleListDetail(id);
      }
      MessagePlugin.success('导入成功');
    } else {
      MessagePlugin.error('导入失败');
    }
  });
};

// 编辑审查项
const handleEditReviewItem = () => {
  activeReviewItem.value.isEdit = true;
};
// 取消编辑审查项
const handleCancelEditReviewItem = () => {
  activeReviewItem.value.isEdit = false;
  getReviewItemDetail(activeReviewItem.value.id);
};

const handleBack = () => {
  router.push('/contract-review/rule-configuration');
};

onMounted(async () => {
  const id = route.params.id as string;
  if (id) {
    await getRuleListDetail(id);
  }
});
</script>
<style scoped lang="less">
@import '../less/review.less';
</style>
