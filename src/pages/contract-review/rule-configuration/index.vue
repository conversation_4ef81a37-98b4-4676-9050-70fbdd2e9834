<template>
  <div v-loading="loading" class="rule-configuration">
    <t-card class="list-card-container">
      <t-row justify="space-between" style="margin-bottom: 16px">
        <t-col>
          <div class="route-title">
            {{ route.meta.title }}
          </div>
        </t-col>
        <t-col>
          <t-button v-permission="menuBlockList.includes('ADD')" theme="primary" @click="onCreateRule">
            新建清单
          </t-button>
        </t-col>
      </t-row>

      <t-table
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        :header-affixed-top="true"
        :fixed-rows="[1, 0]"
        row-key="id"
        @page-change="onPageChange"
      >
        <template #name="{ row }">
          <t-link theme="primary" hover="color" @click="onDetail(row)">{{ row.name }}</t-link>
          <t-tag
            v-if="row.sourceType === 'SYSTEM'"
            style="margin-left: var(--td-comp-margin-s)"
            size="small"
            shape="round"
            variant="outline"
            theme="primary"
          >
            内置
          </t-tag>
        </template>
        <template #createTime>
          创建时间
          <!-- <t-tooltip theme="primary" :duration="3000" content="这是Tooltip内容">
            <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
          </t-tooltip> -->
        </template>
        <template #updateTime>
          最后更新时间
          <!-- <t-tooltip theme="primary" :duration="3000" content="这是Tooltip内容">
            <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
          </t-tooltip> -->
        </template>
        <template #id="{ row }">
          {{ row.id?.padStart(5, '0') }}
        </template>
        <template #op="{ row }">
          <t-space>
            <t-link theme="primary" hover="color" @click="onDetail(row)">查看详情</t-link>
            <!-- 超管功能 -->
            <t-link
              v-if="isAdmin"
              v-permission="menuBlockList.includes('OPEN')"
              theme="primary"
              hover="color"
              @click="onOpenManagement(row)"
            >
              开放管理
            </t-link>
            <t-popconfirm
              v-if="row.sourceType !== 'SYSTEM' || isAdmin"
              theme="danger"
              :content="`确定要删除规则清单 ${row.name} 吗？`"
              @confirm="onDelete(row)"
            >
              <t-link v-permission="menuBlockList.includes('DELETE')" theme="danger" hover="color">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <rule-form-dialog
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :initial-data="formData"
      @confirm="onDialogConfirm"
    />
  </div>
</template>
<script lang="ts">
export default {
  name: 'RuleConfiguration',
};
</script>
<script lang="ts" setup>
// import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import type { PageInfo, PrimaryTableCol } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onActivated, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { deleteById, pageByVo, save, updateById } from '@/api/modules/rule';
import type { PageByVo as PageByVoType, SaveRequest, UpdateByIdRequest } from '@/api/types/rule';
import { useUserStore } from '@/store';
import { RuleVoDetail } from '@/types/review';

import RuleFormDialog from './components/RuleFormDialog.vue';
// 获取当前路由信息
const route = useRoute();
const router = useRouter();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

const {
  userInfo: { ownerStatus, adminStatus },
} = useUserStore();
const isAdmin = computed(() => {
  return ownerStatus === 1 || adminStatus === 1;
});
interface PaginationState {
  total: number;
  pageSize: number;
  current: number;
}
const columns: PrimaryTableCol[] = [
  { colKey: 'name', title: '审查清单', minWidth: 200, ellipsis: true, fixed: 'left' },
  { colKey: 'id', title: '清单ID', width: 200 },
  { colKey: 'ruleCount', title: '规则数', width: 100 },
  { colKey: 'createdTime', title: 'createTime', width: 200 },
  { colKey: 'updatedTime', title: 'updateTime', width: 200 },
  { colKey: 'op', title: '操作', width: isAdmin.value ? 260 : 160, fixed: 'right' },
];
const loading = ref(false);
const tableData = ref<RuleVoDetail[]>([]);
const pagination = reactive<PaginationState>({
  total: 0,
  pageSize: 10,
  current: 1,
});

const dialogVisible = ref(false);
const isEdit = ref(false);
const formData = reactive<SaveRequest>({
  id: '',
  name: '',
  description: '',
});

// 搜索
const onSearch = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    await fetchTableData();
  } finally {
    loading.value = false;
  }
};
// 获取表格数据
const fetchTableData = async () => {
  try {
    const params: PageByVoType = {
      current: pagination.current,
      size: pagination.pageSize,
    };
    const res = await pageByVo(params);
    res?.records?.forEach((item: any) => {
      item.ruleCount = (item.ruleVos || []).length;
    });
    tableData.value = res.records || [];
    pagination.total = Number(res.total) || 0;
  } catch (error) {
    /* empty */
  }
};
// 分页
const onPageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  if (pageInfo.pageSize) pagination.pageSize = pageInfo.pageSize;
  onSearch();
};

// 查看详情
const onDetail = (row: RuleVoDetail) => {
  router.push(`/contract-review/create-rule/${row.id}`);
};
// 新建清单
const onCreateRule = () => {
  isEdit.value = false;
  formData.id = '';
  formData.name = '';
  formData.description = '';
  dialogVisible.value = true;
};
const onOpenManagement = (row: RuleVoDetail) => {
  router.push(`/contract-review/open-management/${row.id}`);
};
// 删除清单
const onDelete = async (row: RuleVoDetail) => {
  deleteById(row.id).then((res) => {
    if (!res) {
      MessagePlugin.error('删除失败');
      return;
    }
    MessagePlugin.success('删除成功');
    onSearch();
  });
};
// 弹窗确认
const onDialogConfirm = async (formData: SaveRequest) => {
  // 防止重复提交
  if (loading.value) return;

  loading.value = true;
  try {
    let res: any;

    if (isEdit.value) {
      const updateParams: UpdateByIdRequest = {
        ...formData,
        id: formData.id,
      };
      res = await updateById(updateParams);
    } else {
      res = await save(formData);
    }

    if (!res) {
      MessagePlugin.error(isEdit.value ? '编辑失败' : '创建失败');
      return;
    }

    MessagePlugin.success(isEdit.value ? '编辑成功' : '创建成功');
    dialogVisible.value = false;
    loading.value = false;
    onSearch();
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch();
});
onActivated(() => {
  onSearch();
});
</script>

<style lang="less" scoped>
.rule-configuration {
  padding: 0;
  .list-card-container {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);

    :deep(.t-card__body) {
      padding: 0;
    }
  }
  .route-title {
    height: 22px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
}
</style>
