<template>
  <t-dialog
    header="从其他清单导入"
    :close-on-overlay-click="false"
    :visible="visible"
    width="800px"
    @close="handleClose"
  >
    <div class="import-dialog">
      <div class="section">
        <div class="section-title">选择审查清单</div>
        <div class="section-content">
          <t-select
            v-model="ruleValue"
            style="width: 300px"
            placeholder="请选择审查规则"
            filterable
            clearable
            :options="selectOptions"
            @search="getRules"
            @change="handleRuleChange"
            @clear="handleRuleClear"
          >
          </t-select>
        </div>
      </div>

      <div class="section">
        <div class="section-title">选择导入规则</div>
        <div class="section-content">
          <t-checkbox-group
            v-model="includedRules"
            class="rules-grid"
            style="width: 100%"
            @change="handleCheckboxChange"
          >
            <t-checkbox
              v-for="(item, index) in checkboxOptions"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></t-checkbox>
          </t-checkbox-group>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="selection-info">
          <t-checkbox label="全选" :indeterminate="indeterminate" :checked="isAllSelected" @change="handleSelectAll" />
          <span class="count">已选 {{ includedRules.length }} 项</span>
        </div>
        <div class="buttons">
          <t-button theme="default" @click="handleClose">取消</t-button>
          <t-button theme="primary" :disabled="includedRules.length === 0" @click="handleConfirm"> 开始导入 </t-button>
        </div>
      </div>
    </template>
  </t-dialog>
</template>
<script setup lang="ts">
import debounce from 'lodash/debounce';
import { computed, onMounted, ref } from 'vue';

import { pageByVo } from '@/api/modules/rule';
import { RuleVoDetail } from '@/types/review';

const props = defineProps<{
  visible: boolean;
  currentRuleDetail: RuleVoDetail;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: { id: string; value: string[] }): void;
}>();
const ruleValue = ref('');
const includedRules = ref([]);
// 存储下拉选择框的选项数据（规则列表）
const selectOptions = ref([]);

// 存储复选框组的选项数据（规则详情中的每一项）
const checkboxOptions = ref([]);
// 控制"全选"复选框的状态
const isAllSelected = ref(false);

// 存储当前选中的规则对象（包含规则名称和子项）
const activeRules = ref({});

// 计算属性：判断复选框是否处于"部分选中"状态（用于控制"全选"组件 indeterminate 状态）
const indeterminate = computed(() => {
  const checkedCount = includedRules.value.length;
  return checkedCount > 0 && checkedCount < checkboxOptions.value.length;
});

/**
 * 搜索规则时触发的方法，使用 debounce 防抖处理输入搜索
 * @param keywords - 用户输入的搜索关键词
 */
const getRules = debounce((keywords: string) => {
  fetchTableData(keywords);
}, 250);

/**
 * 异步获取规则数据
 * 根据关键词搜索规则，并将返回结果映射成 { label, value } 格式供下拉选择器使用
 * 如果已有 reviewRuleListId，则保留当前选中规则
 * @param keywords - 搜索关键词
 */
const fetchTableData = async (keywords: string) => {
  try {
    const params = {
      current: 1,
      size: 30,
      name: keywords,
    };
    const res = await pageByVo(params);

    // 将接口返回的数据格式化为 { label, value, ruleVos } 结构，便于后续使用
    const records = res.records
      .map((item: any) => ({
        label: item.name,
        value: item.id,
        ruleVos: item.ruleVos,
      }))
      .filter((item: any) => item.value !== props.currentRuleDetail.id);

    // 如果当前已有选中的规则列表 ID，将其重新加入选项中（防止被过滤掉）
    if (ruleValue.value) {
      records.push(activeRules.value);
    }

    selectOptions.value = records || [];
  } catch (error) {
    // 错误处理，提示用户具体错误信息
    console.error(error);
  }
};
/**
 * 下拉选择规则时触发的事件处理函数
 * 根据选中的规则 ID 获取对应的规则列表，并更新复选框选项和选中状态
 * @param value - 选中的规则 ID
 */
const handleRuleChange = (value: any) => {
  if (!value) return;

  // 查找选中的规则对象
  const item = selectOptions.value.find((item: any) => item.value === value);
  if (item) {
    activeRules.value = item;
    const ruleVos = item.ruleVos || [];

    // 更新规则数量、全选状态、表单数据、复选框选项
    isAllSelected.value = true;
    includedRules.value = ruleVos.map((rule: any) => rule.id);
    checkboxOptions.value = ruleVos.map((rule: any) => ({
      label: rule.name,
      value: rule.id,
    }));
  } else {
    // 如果未找到对应规则，重置相关状态
    handleRuleClear();
  }
};

/**
 * 清除规则选择时触发的事件处理函数
 * 重置所有与规则相关的状态
 */
const handleRuleClear = () => {
  activeRules.value = {};
  isAllSelected.value = false;
  includedRules.value = [];
  checkboxOptions.value = [];
};

/**
 * 全选复选框点击事件
 * 控制是否选中所有规则项
 * @param checked - 是否选中
 */
const handleSelectAll = (checked: boolean) => {
  isAllSelected.value = checked;
  if (checked) {
    includedRules.value = checkboxOptions.value.map((item: any) => item.value);
  } else {
    includedRules.value = [];
  }
};

/**
 * 复选框组变更事件
 * 当用户勾选部分规则时更新"全选"状态
 * @param value - 当前选中的规则值数组
 */
const handleCheckboxChange = (value: any[]) => {
  // 判断是否全部选中
  isAllSelected.value = value.length === checkboxOptions.value.length;
};

const handleConfirm = () => {
  emit('confirm', { id: ruleValue.value, value: includedRules.value });
  emit('update:visible', false);
};

const handleClose = () => {
  emit('update:visible', false);
};
onMounted(() => {
  fetchTableData('');
});
</script>

<style scoped lang="less">
.import-dialog {
  .section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .section-content {
      .rules-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .selection-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .count {
      color: var(--el-text-color-secondary);
    }
  }
}
</style>
