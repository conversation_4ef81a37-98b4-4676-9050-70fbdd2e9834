<template>
  <t-dialog
    width="800px"
    :visible="visible"
    :close-on-overlay-click="false"
    :header="isEdit ? '编辑审查示例' : '新建审查示例'"
    :confirm-btn="{ content: '确定', theme: 'primary' }"
    :cancel-btn="{ content: '取消' }"
    @confirm="onDialogConfirm"
    @close="onClose"
  >
    <t-form ref="form" :data="formData" :rules="rules" label-align="top">
      <t-form-item label-align="left" label-width="80" label="审查项：">{{ name }}</t-form-item>
      <t-form-item label="合同内容：" name="contractContent">
        <t-textarea
          v-model="formData.contractContent"
          :autosize="{ minRows: 4 }"
          placeholder="请输入合同内容的示例，AI将学习您所提供的合同内容，提升风险审查的准确度。"
          maxlength="2000"
          autofocus
        />
      </t-form-item>
      <t-form-item label="正确审查结果：" name="correctResult">
        <t-radio-group v-model="formData.correctResult">
          <t-radio value="NONE">无风险</t-radio>
          <t-radio value="RISK">存在风险</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="审查说明：" name="reviewDescription">
        <t-textarea
          v-model="formData.reviewDescription"
          :autosize="{ minRows: 4 }"
          placeholder="请说明该合同内容存在风险或无风险的理由说明，AI将进行学习。"
          maxlength="500"
          autofocus
        />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script lang="ts" setup>
import type { FormRule } from 'tdesign-vue-next';
import { ref, watch } from 'vue';

import type { ExampleList } from '@/types/review';

const props = defineProps<{
  visible: boolean;
  isEdit: boolean;
  name: string | null | undefined;
  initialData?: ExampleList;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: ExampleList): void;
}>();

const form = ref();
const formData = ref<any>({
  id: '',
  contractContent: '',
  correctResult: 'NONE',
  reviewDescription: '',
  ruleOrigin: 'FEEDBACK',
});

const rules: Record<string, FormRule[]> = {
  contractContent: [{ required: true, message: '请输入合同内容的示例', type: 'error' }],
  correctResult: [{ required: true, message: '请选择正确审查结果', type: 'error' }],
  reviewDescription: [{ required: true, message: '请说明该合同内容存在风险或无风险的理由说明', type: 'error' }],
};

// 监听初始数据变化
watch(
  props.initialData,
  (newVal) => {
    if (newVal && props.isEdit) {
      formData.value = { ...newVal };
    }
  },
  { immediate: true },
);

const onDialogConfirm = async () => {
  const validateResult = await form.value.validate();
  if (validateResult === true) {
    emit('confirm', formData.value);
  }
};

const onClose = () => {
  emit('update:visible', false);
};
</script>
