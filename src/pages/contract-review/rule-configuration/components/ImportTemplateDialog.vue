<template>
  <t-dialog
    header="模板批量导入字段"
    :close-on-overlay-click="false"
    :visible="visible"
    width="700px"
    :cancel-btn="{ content: '取消' }"
    @confirm="handleClose"
    @close="handleClose"
  >
    <t-upload
      ref="uploadRef"
      class="upload-file"
      :action="uploadUrl"
      :headers="headers"
      accept=".xls,.xlsx"
      :format-response="formatResponse"
      :mock-progress-duration="100"
      :multiple="false"
      draggable
      theme="custom"
      tips="支持xls/xlsx格式"
      allow-upload-duplicate-file
      @select-change="selectChange"
      @success="onUploadSuccess"
      @remove="onUploadRemove()"
      @fail="onUploadFail"
      @progress="onProgress"
    >
      <template #dragContent>
        <div v-if="!fileLength && !errorValue" class="import-dialog">
          <div class="upload-file-wrapper">
            <div class="upload-file-wrapper-img"><catalog-icon /></div>
            <div class="upload-file-wrapper-title">点击或将文件拖拽到这里上传</div>
            <div class="upload-file-wrapper-p">
              下载模板文件,并按照模板格式填写规则信息后上传,支持xlsx格式。单次最多导入200条，清单中不允许存在名称相同的字段，如名称相同将不会被导入。
            </div>
            <t-button variant="outline" @click.stop="handleDownloadTemplate">
              <template #icon>
                <cloud-download-icon />
              </template>
              下载模板
            </t-button>
          </div>
        </div>
        <div v-else-if="progress < 100 && progress > 0" class="import-dialog">
          <div class="upload-file-wrapper">
            <div class="upload-file-wrapper-img"><catalog-icon /></div>
            <div class="upload-file-wrapper-title">规则解析中...</div>
            <div class="upload-file-wrapper-p">进度{{ progress }}%，在此期间请勿关闭当前页面，感谢您的耐心等待。</div>
            <div class="upload-file-wrapper-p" style="width: 400px">
              <t-progress :percentage="progress" theme="line" :size="30" status="active">
                <template #label>
                  <div v-if="progress < 100" class="cancel-btn" @click.stop="cancelUpload">取消</div>
                </template>
              </t-progress>
            </div>
          </div>
        </div>
        <div v-else-if="progress === 100 && fileLength" class="import-dialog">
          <div class="upload-file-wrapper">
            <div class="upload-file-wrapper-img"><catalog-icon /></div>
            <div class="upload-file-wrapper-title">导入成功</div>
            <div class="upload-file-wrapper-p">本次共导入{{ fileLength }}个字段</div>
          </div>
        </div>
        <div v-else-if="errorValue" class="import-dialog">
          <div class="upload-file-wrapper">
            <div class="upload-file-wrapper-img err"><catalog-icon /></div>
            <div class="upload-file-wrapper-title">文件解析失败</div>
            <div v-if="errorMsg" class="upload-file-wrapper-p red">{{ errorMsg }}</div>
          </div>
        </div>
      </template>
    </t-upload>
  </t-dialog>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { CatalogIcon, CloudDownloadIcon } from 'tdesign-icons-vue-next';
import { ProgressContext, UploadFailContext } from 'tdesign-vue-next';
import { ref } from 'vue';

import { useUserStore } from '@/store';
import { RuleVoDetail } from '@/types/review';

const props = defineProps<{
  visible: boolean;
  currentRuleDetail: RuleVoDetail;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: any): void;
}>();

const fileLength = ref(0);

// 获取上传组件实例的引用，用于操作上传行为（如取消上传）
const uploadRef = ref();
// 上传进度百分比，初始值为0%
const progress = ref(0);
// 控制是否显示错误信息的状态变量
const errorValue = ref(false);
// 存储错误信息的变量
const errorMsg = ref('');
// 定义文件上传的目标URL，从环境变量中获取API基础路径并拼接上传接口地址
const uploadUrl = `${import.meta.env.VITE_API_URL_PREFIX}/reviewRule/importExcel/${props.currentRuleDetail.id}`;
// 从Pinia store中解构出token，用于携带认证信息
const { token } = storeToRefs(useUserStore());
// 请求头配置，包含鉴权Token
const headers = {
  [import.meta.env.VITE_TOKEN_NAME]: token.value, // 使用环境变量定义的Token键名
};
// 格式化上传响应数据，将后端返回的数据结构转换为upload组件所需的格式
const formatResponse = (res: any) => {
  if (res.resultCode === 0) {
    return { id: res.data }; // 成功时返回文件ID
  }
  return { error: res.errMessage || res.XMLHttpRequest.statusText }; // 失败时返回错误信息
};
// 文件上传成功回调函数
const onUploadSuccess = (context: any) => {
  const { file } = context; // SuccessContext 中 file 可能是可选的
  if (file) {
    fileLength.value = file.response?.id || 0;
    errorValue.value = false; // 清除错误状态
    errorMsg.value = ''; // 清空错误信息
    setTimeout(() => {
      progress.value = 100; // 模拟上传完成进度
    }, 1000);
  }
};

// 文件移除回调函数，清空已上传的文件信息
const onUploadRemove = () => {
  fileLength.value = 0;
  progress.value = 0; // 进度条重置为0%
  errorValue.value = false; // 清除错误状态
  errorMsg.value = ''; // 清空错误信息
};

// 文件上传失败回调函数
const onUploadFail = (options: UploadFailContext) => {
  const { response } = options;
  fileLength.value = 0;
  progress.value = 0; // 进度条重置
  errorValue.value = true; // 显示错误状态
  errorMsg.value = response.error; // 设置错误信息
};

// 上传进度监听函数，更新当前上传进度百分比
const onProgress = (options: ProgressContext) => {
  const { percent } = options;
  progress.value = percent; // 更新进度条数值
};

// 取消上传操作
const cancelUpload = () => {
  onUploadRemove();
  uploadRef.value?.cancelUpload(); // 调用上传组件的取消方法
};

// 文件选择改变事件，触发上传开始
const selectChange = () => {
  progress.value = 1; // 开始上传时设置一个非零值以显示进度区域
};

const handleClose = () => {
  if (fileLength.value) {
    emit('confirm', {});
  }
  emit('update:visible', false);
};

const handleDownloadTemplate = () => {
  window.open(`/template/审查规则导入模板.xlsx`);
};
</script>

<style scoped lang="less">
.column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.upload-file::v-deep {
  width: 100%;
  height: 300px;
  .t-upload__dragger-center {
    padding: 0;
    height: 280px;
    width: 100%;
    .t-upload__trigger {
      width: 100%;
    }
  }
}
.import-dialog {
  height: 278px;
  background-color: #f7f8fc;
  .column(); // 使用 mixin 替代 @extend

  .upload-file-wrapper {
    width: 520px;
    .column(); // 使用 mixin 替代 @extend
    .upload-file-wrapper-img {
      .t-icon {
        font-size: 40px;
        color: var(--td-brand-color);
      }
      &.err {
        .t-icon {
          color: #bec2d2;
        }
      }
    }
    .upload-file-wrapper-title {
      font-size: 16px;
      font-weight: bold;
      margin: 15px 0;
    }
    .upload-file-wrapper-p {
      text-align: center;
      margin-bottom: 20px;
      &.red {
        color: var(--td-error-color-5);
      }
    }
    .cancel-btn {
      color: var(--td-error-color-5);
      font-size: 12px;
      cursor: pointer;
    }
  }
}
</style>
