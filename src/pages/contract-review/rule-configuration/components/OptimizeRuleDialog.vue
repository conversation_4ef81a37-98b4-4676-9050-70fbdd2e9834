<template>
  <t-dialog
    width="800px"
    :visible="visible"
    header="智能规则优化"
    :close-on-overlay-click="false"
    :confirm-btn="{ content: '应用优化结果', theme: 'primary', disabled: !optimizationResult.result }"
    :cancel-btn="{ content: '取消' }"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <t-form class="optimize-dialog">
      <t-form-item label="审查项：">{{ initialData?.name }} </t-form-item>
      <t-form-item label="规则描述：">
        <t-textarea
          v-model="rule"
          :autosize="{ minRows: 4 }"
          placeholder="请输入审查规则，AI将基于您的描述进行优化。"
          maxlength="1000"
        />
      </t-form-item>
      <t-form-item label="智能优化：">
        <t-button theme="primary" variant="outline" :loading="isOptimizing" @click="handleOptimize">
          {{ isOptimizing ? '优化中...' : !optimizationResult.result ? '开始优化' : '重新优化' }}
        </t-button>
      </t-form-item>
      <template v-if="optimizationResult.result">
        <!-- <t-form-item label="优化建议：">{{ optimizationResult.suggestion }} </t-form-item> -->
        <t-form-item label="优化结果：">{{ optimizationResult.result }} </t-form-item>
        <t-form-item label="重要提示：">
          <span class="tips">
            AI仅基于规则结构及已有知识进行基础优化，结果仅供参考。<br />高质量的审查效果，取决于对业务规则清晰、详细的定义与描述，建议您在AI优化的基础上，检查AI对业务规则描述的准确性，并结合实际业务知识再次进行优化调整。
          </span>
        </t-form-item>
      </template>
    </t-form>
  </t-dialog>
</template>
<script setup lang="ts">
import { onBeforeUnmount, ref, watch } from 'vue';

import { intelligentOptimization } from '@/api/modules/rule';
import type { OptimizationResult } from '@/types/review';

const props = defineProps<{
  visible: boolean;
  initialData: any;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', result: string): void;
}>();

const rule = ref('');
// 监听初始数据变化
watch(
  () => props.initialData,
  (newVal) => {
    if (newVal) {
      rule.value = newVal.rule;
    }
  },
  { immediate: true, deep: true },
);
const isOptimizing = ref(false);
const optimizationResult = ref<OptimizationResult>({
  suggestion: '',
  result: '',
});
const timer = ref(-1);
let remainText: string = ''; // 未显示的内容
// 打字机效果
const animateResponseText = () => {
  timer.value = window.setInterval(() => {
    if (!remainText) {
      optimizationResult.value.result += remainText;
      clearInterval(timer.value);
    } else {
      optimizationResult.value.result += remainText.slice(0, 1);
      remainText = remainText.slice(1);
    }
  }, 50);
};
// 优化功能
const handleOptimize = async () => {
  reset();
  isOptimizing.value = true;
  const params = {
    name: props.initialData?.name,
    rule: rule.value,
  };
  intelligentOptimization(params)
    .then((res: any) => {
      remainText = res;
      animateResponseText();
    })
    .finally(() => {
      isOptimizing.value = false;
    });
};
// 确认
const handleConfirm = () => {
  emit('confirm', optimizationResult.value.result);
  handleClose();
};
// 重置参数
const reset = () => {
  isOptimizing.value = false;
  optimizationResult.value = {
    suggestion: '',
    result: '',
  };
  remainText = '';
  clearInterval(timer.value);
};
// 离开当前组件
const handleClose = () => {
  reset();
  emit('update:visible', false);
};
// 离开当前组件销毁timeOut
onBeforeUnmount(() => {
  reset();
});
</script>
<style scoped lang="less">
.optimize-dialog {
  .tips {
    font-size: 12px;
    color: var(--td-warning-color-5);
  }
}
</style>
