<template>
  <t-dialog
    :visible="visible"
    :header="isEdit ? '编辑清单' : '新建清单'"
    :close-on-overlay-click="false"
    :confirm-btn="{ content: '确定', theme: 'primary' }"
    :cancel-btn="{ content: '取消' }"
    @confirm="onDialogConfirm"
    @close="onClose"
  >
    <t-form ref="form" :data="formData" :rules="rules">
      <t-form-item label="清单名称" name="name">
        <t-input v-model="formData.name" maxlength="30" show-limit-number placeholder="请输入清单名称" />
      </t-form-item>
      <t-form-item label="清单描述" name="description">
        <t-textarea v-model="formData.description" maxlength="500" show-limit-number placeholder="请输入清单描述" />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script lang="ts" setup>
import type { FormRule } from 'tdesign-vue-next';
import { ref, watch } from 'vue';

import type { SaveRequest } from '@/api/types/rule';

const props = defineProps<{
  visible: boolean;
  isEdit: boolean;
  initialData?: SaveRequest;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: SaveRequest): void;
}>();

const form = ref();
const formData = ref<SaveRequest>({
  id: '',
  name: '',
  description: '',
});

const rules: Record<string, FormRule[]> = {
  name: [{ required: true, message: '请输入清单名称', type: 'error' }],
};

// 监听初始数据变化
watch(
  props.initialData,
  (newVal) => {
    if (newVal && props.isEdit) {
      formData.value = { ...newVal };
    }
  },
  { immediate: true },
);

const onDialogConfirm = async () => {
  const validateResult = await form.value.validate();
  if (validateResult === true) {
    emit('confirm', formData.value);
    form.value.reset();
  }
};

const onClose = () => {
  form.value.reset();
  emit('update:visible', false);
};
</script>
