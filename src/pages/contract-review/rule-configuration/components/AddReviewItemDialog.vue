<template>
  <t-dialog
    :header="title"
    :visible="visible"
    width="500px"
    :close-on-overlay-click="false"
    :confirm-btn="{ content: '确定', theme: 'primary' }"
    :cancel-btn="{ content: '取消' }"
    @confirm="handleConfirm"
    @close="onClose"
  >
    <div class="add-review-dialog">
      <t-input
        v-model="reviewContent"
        placeholder="请输入审查项名称"
        maxlength="50"
        :status="status"
        :tips="tips"
        autofocus
        clearable
        show-limit-number
      />
    </div>
  </t-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

const props = defineProps<{
  visible: boolean;
  title: string;
  name?: string | undefined;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'update:name', value: string): void;
  (e: 'confirm', data: any): void;
}>();

const status = ref<'default' | 'warning' | 'success' | 'error'>('default');
const tips = ref('');
const reviewContent = ref('');

// 监听name变化
watch(
  () => props.name,
  async (newVal) => {
    reviewContent.value = newVal;
  },
  { immediate: true },
);
const handleConfirm = () => {
  if (!reviewContent.value) {
    status.value = 'error';
    tips.value = '请输入审查项名称';
    return;
  }
  status.value = 'default';
  tips.value = '';
  emit('confirm', reviewContent.value);
};
const onClose = () => {
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.add-review-dialog {
  padding: 20px 0;

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
  }
}
</style>
