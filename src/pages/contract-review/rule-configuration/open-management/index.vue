<template>
  <div v-loading="loading" class="new-rule-page">
    <t-card class="list-card-container">
      <div class="route-title">
        {{ route.meta.title }}
      </div>

      <div class="page-content open-management">
        <div class="rule-name">{{ currentRuleDetail?.name }}</div>
        <t-form
          ref="ruleForm"
          :key="currentRuleDetail.isEdit ? 'detail' : 'edit'"
          :disabled="currentRuleDetail.isEdit"
          class="form-container"
          label-align="top"
          :data="formData"
          :rules="currentRuleDetail.isEdit ? {} : rules"
        >
          <t-form-item name="share">
            <template #label>
              <div class="form-title">
                开放状态&nbsp;
                <t-tooltip theme="primary" :duration="3000" content="开放后，您可以将自己创建的审查规则共享给其他用户">
                  <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
                </t-tooltip>
              </div>
            </template>
            <t-radio-group v-model="formData.share" @change="handleRadioChange">
              <t-radio :value="1">开放</t-radio>
              <t-radio :value="0">不开放</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item name="shareRuleIds">
            <template #label>
              <div class="form-title">开放内容</div>
            </template>
            <div>
              <div class="selection-info">
                <t-checkbox
                  v-model="formData.ruleShareAll"
                  :label="`全选(${(currentRuleDetail?.ruleVos || []).length})`"
                  :indeterminate="indeterminate"
                  @change="handleSelectAll"
                />
                <span class="count">已选 {{ formData.shareRuleIds.length }} 项</span>
              </div>
              <t-checkbox-group
                v-model="formData.shareRuleIds"
                class="rules-grid"
                style="width: 100%"
                @change="handleCheckboxChange"
              >
                <t-checkbox
                  v-for="(item, index) in currentRuleDetail?.ruleVos || []"
                  :key="index"
                  :value="item.id"
                  :label="item.name"
                ></t-checkbox>
              </t-checkbox-group>
            </div>
          </t-form-item>
          <t-form-item name="shareScope">
            <template #label>
              <div class="form-title">开放范围</div>
            </template>
            <div>
              <t-radio-group v-model="formData.shareScope" class="radio-group">
                <t-radio value="PRIVATE">不开放</t-radio>
                <t-radio value="ALL">全部开放</t-radio>
                <t-radio value="PART">
                  指定租户开放
                  <t-button
                    v-show="formData.shareScope === 'PART' && !currentRuleDetail.isEdit"
                    style="margin-left: 10px"
                    theme="primary"
                    size="small"
                    @click="handleTenantOpen"
                  >
                    添加租户
                  </t-button>
                </t-radio>
              </t-radio-group>
              <div
                v-show="formData.shareScope === 'PART'"
                :class="['tenant-name', { disabled: currentRuleDetail.isEdit }]"
              >
                {{ computeTenantName }}
              </div>
            </div>
          </t-form-item>
        </t-form>
        <div class="btn-container">
          <t-button v-if="currentRuleDetail.isEdit" theme="primary" @click="handleManagement"> 编辑 </t-button>
          <template v-else>
            <t-button theme="primary" @click="handleSave"> 保存 </t-button>
            <t-button theme="default" @click="handleClose"> 取消 </t-button>
          </template>
        </div>
      </div>
    </t-card>

    <!-- 租户选择框 -->
    <tenant-dialog
      v-if="visible"
      v-model:visible="visible"
      v-model:values="formData.shareTenants"
      :rule-id="currentRuleDetail.id"
      @confirm="handleConfirm"
    ></tenant-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'OpenManagement',
};
</script>
<script setup lang="ts">
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import { FormRule, MessagePlugin, RadioValue } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { getById, shareConfig } from '@/api/modules/rule';
import TenantDialog from '@/components/tenant-dialog/index.vue';
import { RuleVoDetail } from '@/types/review';

const route = useRoute(); // 获取当前路由信息
const loading = ref(false); // 页面加载状态控制
const ruleForm = ref(); // 表单引用，用于表单校验等操作
// 表单数据模型
const formData = ref({
  share: 1,
  shareRuleIds: [],
  ruleShareAll: false,
  shareScope: 'PRIVATE',
  shareTenants: [],
});
// 计算属性：显示已选租户名称
const computeTenantName = computed(() => {
  if (formData.value.shareTenants.length === 0) {
    return '无';
  }
  return formData.value.shareTenants.map((ele) => ele.tenantName).join('、');
});
// 表单原始数据副本，用于取消或保存操作
const copyFormData = ref(null);
const rules: Record<string, FormRule[]> = {
  share: [{ required: true, message: '请选择开放状态', type: 'error' }],
  shareRuleIds: [{ required: true, message: '请选择开放内容', type: 'error' }],
  shareScope: [{ required: true, message: '请选择开放范围', type: 'error' }],
};

/** 当前清单的详细信息 */
const currentRuleDetail = ref<RuleVoDetail>({
  id: '',
  name: '',
  ruleVos: [],
  isEdit: true,
});
const handleRadioChange = (value: RadioValue) => {
  if (value === 0) {
    formData.value.shareScope = 'PRIVATE';
  }
};
/** 获取规则清单详情 */
const getRuleListDetail = async (id: string) => {
  loading.value = true;
  getById(id)
    .then((data: any) => {
      currentRuleDetail.value = data;
      currentRuleDetail.value.isEdit = true;
      const { ruleVos } = data;
      if (ruleVos?.length) {
        const ids = ruleVos.filter((item: any) => item.share === 1).map((item: any) => item.id);
        formData.value.shareRuleIds = ids;
        formData.value.ruleShareAll = ids.length === ruleVos.length;
      } else {
        formData.value.shareRuleIds = [];
        formData.value.ruleShareAll = false;
      }

      formData.value.share = data.share || 0;
      formData.value.shareScope = data.shareScope || 'PRIVATE';
      formData.value.shareTenants = data.shareTenants || [];
    })
    .finally(() => {
      loading.value = false;
    });
};

// 点击管理按钮开启编辑模式
const handleManagement = () => {
  copyFormData.value = { ...formData.value };
  currentRuleDetail.value.isEdit = false;
};
// 点击保存按钮保存数据
const handleSave = async () => {
  const validateResult = await ruleForm.value.validate();
  if (validateResult === true) {
    loading.value = true;
    const params = {
      ...formData.value,
      listId: currentRuleDetail.value.id,
    };
    shareConfig(params)
      .then((res) => {
        if (res) currentRuleDetail.value.isEdit = true;
        else MessagePlugin.error(`保存失败`);
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
// 点击取消按钮取消数据
const handleClose = () => {
  formData.value = { ...copyFormData.value };
  currentRuleDetail.value.isEdit = true;
};

// 计算属性：判断是否部分选中（indeterminate状态）
const indeterminate = computed(() => {
  const checkedCount = formData.value.shareRuleIds.length;
  return checkedCount > 0 && checkedCount < (currentRuleDetail.value.ruleVos || []).length;
});
// 全选操作处理
const handleSelectAll = (checked: boolean) => {
  formData.value.ruleShareAll = checked;
  if (checked) {
    formData.value.shareRuleIds = (currentRuleDetail.value.ruleVos || []).map((item: any) => item.id);
  } else {
    formData.value.shareRuleIds = [];
  }
};
// 复选框组变更处理
const handleCheckboxChange = (value: any[]) => {
  // 判断是否全部选中
  formData.value.ruleShareAll = value.length === (currentRuleDetail.value.ruleVos || []).length;
};

const visible = ref(false); // 控制添加租户对话框的可见性
// 打开租户选择对话框，初始化已选租户数据
const handleTenantOpen = () => {
  visible.value = true;
};
// 确认租户选择
const handleConfirm = (data: any) => {
  formData.value.shareTenants = data;
};

onMounted(async () => {
  const id = route.params.id as string;
  if (id) {
    await getRuleListDetail(id);
  }
});
</script>
<style scoped lang="less">
@import '../less/review.less';
@import '../less/open-management.less';
</style>
