/**
 * 表格列配置
 */
export const TABLE_COLUMNS: any = [
  { colKey: 'fileName', title: '合同名称', minWidth: 200, ellipsis: true, fixed: 'left' },
  { colKey: 'createdTime', title: '创建时间', width: 200 },
  { colKey: 'totalDuration', title: '任务总耗时', width: 160 },
  { colKey: 'status', title: '状态', width: 130 },
  { colKey: 'op', title: '操作', width: 300, fixed: 'right' },
];

/**
 * 任务状态枚举
 */
export const STATUS = {
  /**  进行中 */
  ONGOING: 'ONGOING',
  /** 已完成 */
  COMPLETED: 'COMPLETED',
  /** 失败 */
  FAILED: 'FAILED',
  /** 已终止 */
  TERMINATED: 'TERMINATED',
  /** 排队中 */
  QUEUED: 'QUEUED',
  /** 无效 */
  INVALID: 'INVALID',
  /** 审查中 */
  REVIEWING: 'REVIEWING',
  /** 分析中 */
  ANALYSING: 'ANALYSING',
  /** 分析完成 */
  ANALYZED: 'ANALYZED',
} as const;

/**
 * 状态对应的主题样式映射
 */
export const STATUS_THEME_MAP: Record<string, string> = {
  [STATUS.ONGOING]: 'primary',
  [STATUS.COMPLETED]: 'success',
  [STATUS.FAILED]: 'danger',
  [STATUS.TERMINATED]: 'warning',
  [STATUS.QUEUED]: 'default',
  [STATUS.INVALID]: 'default',
  [STATUS.REVIEWING]: 'primary',
  [STATUS.ANALYSING]: 'primary',
  [STATUS.ANALYZED]: 'primary',
};

/**
 * 状态对应的文本显示映射
 */
export const STATUS_TEXT_MAP: Record<string, string> = {
  [STATUS.ONGOING]: '进行中',
  [STATUS.COMPLETED]: '已完成',
  [STATUS.FAILED]: '失败',
  [STATUS.TERMINATED]: '已终止',
  [STATUS.QUEUED]: '排队中',
  [STATUS.INVALID]: '无效',
  [STATUS.REVIEWING]: '进行中', // 审查中
  [STATUS.ANALYSING]: '进行中', // 分析中
  [STATUS.ANALYZED]: '进行中', // 分析中
};

/**
 * 审查模式
 */
export const REVIEW_MODE: Record<string, 'HIGH_SPEED' | 'HIGH_QUALITY'> = {
  /** 高精度 */
  HIGH_QUALITY: 'HIGH_QUALITY',
  /** 高性能 */
  HIGH_SPEED: 'HIGH_SPEED',
};
/**
 * 审查模式枚举
 */
export const REVIEW_MODE_ENUM: Record<string, string> = {
  /** 高精度 */
  [REVIEW_MODE.HIGH_QUALITY]: '高精度',
  /** 高性能 */
  [REVIEW_MODE.HIGH_SPEED]: '高性能',
};

/**
 * 审查规则结果
 */
export const REVIEW_STATUS: Record<string, string> = {
  /** 待审查 */
  REVIEWING: 'REVIEWING',
  /** 通过 */
  COMPLETED: 'COMPLETED',
  /** 未通过 */
  FAILED: 'FAILED',
};
/**
 * 审查规则结果状态枚举
 */
export const REVIEW_STATUS_ENUM: Record<string, string> = {
  /** 待审查 */
  [REVIEW_STATUS.REVIEWING]: '待审查',
  /** 通过 */
  [REVIEW_STATUS.COMPLETED]: '通过',
  /** 未通过 */
  [REVIEW_STATUS.FAILED]: '未通过',
};
/**
 * 风险等级
 */
export const RISK_LEVEL: Record<string, string> = {
  /** 无风险 */
  NONE: 'NONE',
  /** 存在风险 */
  RISK: 'RISK',
};
/**
 * 风险等级枚举
 */
export const RISK_LEVEL_ENUM: Record<string, string> = {
  /** 无风险 */
  [RISK_LEVEL.NONE]: '无风险',
  /** 存在风险 */
  [RISK_LEVEL.RISK]: '存在风险',
};

/**
 * 风险等级
 */
export const RISK: Record<string, string> = {
  /** 高风险 */
  HIGH: 'HIGH',
  /** 中风险 */
  MEDIUM: 'MEDIUM',
  /** 低风险 */
  LOW: 'LOW',
  /** 无风险 */
  NONE: 'NONE',
};
/**
 * 风险等级枚举
 */
export const RISK_ENUM: Record<string, string> = {
  /** 高风险 */
  [RISK.HIGH]: '高风险',
  /** 中风险 */
  [RISK.MEDIUM]: '中风险',
  /** 低风险 */
  [RISK.LOW]: '低风险',
  /** 无风险 */
  [RISK.NONE]: '无风险',
};
