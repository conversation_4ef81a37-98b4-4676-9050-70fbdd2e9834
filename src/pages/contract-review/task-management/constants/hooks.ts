import { ref } from 'vue';

import { closeSseConnect, taskSave } from '@/api/modules/task';
import { ReviewRequest, sseKey } from '@/api/types/task';

interface IOnFinish {
  msg?: string;
  isAbort?: boolean;
  isStreaming?: boolean;
  isError?: boolean;
  isFinish?: boolean;
}

interface StreamChatProps {
  id?: string;
  params?: ReviewRequest;
  onOpen?: () => void;
  onMessage?: (obj: any) => void;
  onError?: () => void;
  onFinish?: (obj: IOnFinish) => void;
}

/**
 * 自定义 Hook：useStreamChat
 *
 * 用于处理流式聊天请求（如 SSE - Server-Sent Events），提供发送消息、中断请求以及停止流的功能。
 *
 * @returns 包含以下属性的对象：
 *   - sendMessages: 发送流式请求的方法。
 *   - stopStream: 停止当前流的方法。
 *   - isStreaming: 表示当前是否处于流式传输状态（true 表示正在流式传输）。
 */
export const useStreamChat = () => {
  /**
   * controller
   *
   * 用于控制请求的中断操作（AbortController）。
   * 当前请求正在进行时，可通过此控制器中断请求。
   */
  const controller = ref();

  /**
   * isStreaming
   *
   * 标记当前是否处于流式传输状态。
   * - true: 正在接收服务器流数据。
   * - false: 流已结束或未开始。
   */
  const isStreaming = ref(false);

  /**
   * onFinishFn
   *
   * 存储 onFinish 回调函数的引用，以便在请求结束时触发回调。
   */
  const onFinishFn = ref();

  /**
   * 发起流式请求的核心方法。
   *
   * @param props - 请求参数与回调函数。
   * @param props.params - 请求参数，类型为 ReviewRequest。
   * @param props.id - 可选，用于重连的 ID。
   * @param props.onMessage - 接收服务器消息时的回调函数。
   * @param props.onFinish - 请求完成时的回调函数。
   * @param key
   */
  const sendMessages = ({ params, id, onMessage, onFinish }: StreamChatProps, key?: sseKey) => {
    // 存储 onFinish 回调以供后续使用
    onFinishFn.value = onFinish;

    // 如果已有进行中的请求，则先中止它
    if (controller.value && (key === 'reconnectSse' || !key)) {
      controller.value.abort();
    }

    // 创建新的 AbortController 实例
    controller.value = new AbortController();

    // 设置请求被中断时的回调
    controller.value.signal.onabort = () => {
      onFinish?.({
        msg: '请求已中断，请重试',
        isError: true,
        isFinish: true,
      });
    };

    // 调用 SSE 方法发起流式请求
    taskSave(key, key ? id : params, {
      signal: controller.value.signal,
      // eslint-disable-next-line consistent-return
      async onopen(res: any) {
        const contentType = res.headers.get('content-type');
        const isFail = !res.ok || !contentType?.startsWith('text/event-stream') || res.status !== 200;

        if (contentType?.startsWith('text/plain')) {
          const errorText = await res.clone().text();
          return onMessage?.({ msg: errorText, isFinish: true });
        }

        if (isFail) {
          return onMessage?.({ msg: '获取信息失败，请稍后重试', isFinish: true });
        }
        // 默认返回，保持函数返回一致性
      },
      onmessage({ data }: any) {
        if (!data) return;
        // console.log('sse data', data);
        try {
          const json = JSON.parse(data);
          isStreaming.value = !json.taskFinished;

          if (json.data) {
            const message = JSON.parse(json.data);
            delete message.data;

            onMessage?.({
              msg: JSON.stringify({ ...message, ...json }),
              isFinish: json.taskFinished,
              isStreaming: isStreaming.value,
              taskId: json.taskId,
              taskStatus: json.taskStatus || 'ANALYSING',
              bizType: json.bizType,
              bizStatus: json.bizStatus,
            });
          } else {
            // 空数据响应
            onMessage?.({
              msg: '',
              isError: true,
              taskId: json.taskId,
              taskStatus: json.taskStatus || 'ANALYSING',
              bizType: json.bizType,
              bizStatus: json.bizStatus,
            });
          }
        } catch (err) {
          console.log('JSON 解析失败', err);
        }
      },
      onclose() {
        isStreaming.value = false;
        onFinish?.({ msg: '服务器繁忙，请重试', isFinish: true });
      },
      onerror() {
        isStreaming.value = false;
        controller.value = null;
        onFinish?.({ msg: '服务器繁忙，请重试', isFinish: true });
      },
    });
  };

  // 封装 sendMessages，标记为重连模式
  const reconnectMessages = (props: StreamChatProps, key: sseKey = 'reconnectSse') => {
    sendMessages(props, key);
  };

  /**
   * stopStream
   *
   * 主动停止流式请求的方法。
   *
   * @param id - 需要关闭的 SSE 连接 ID。
   */
  const stopStream = (id: string) => {
    // 主动关闭 SSE 连接
    closeSseConnect(id);

    // 中断当前请求
    controller.value?.abort();

    // 设置 isStreaming 为 false，表示流已结束
    isStreaming.value = false;

    // 触发 onFinish 回调，通知请求已完成
    onFinishFn.value({ isFinish: true });
  };

  // 返回可公开访问的方法和状态
  return {
    sendMessages,
    stopStream,
    isStreaming,
    reconnectMessages,
  };
};
