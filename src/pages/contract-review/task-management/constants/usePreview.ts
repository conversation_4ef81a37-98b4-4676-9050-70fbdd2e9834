declare global {
  interface Window {
    app: any;
  }
}
import { nextTick, ref } from 'vue';

import { fileContent, getEditUrl } from '@/api/modules/task';

// @ts-ignore
import OpenSDK from './openSDK.js';

export function usePreview() {
  const customLoading = ref({
    loading: false,
    showOverlay: true,
    text: '加载中...',
  });
  const instance = ref<any>(null);
  // 文件解析进度百分比，0 ~ 100
  const percentage = ref(0);
  const tabIndex = ref('word');

  const tabClick = (tab: string) => {
    tabIndex.value = tab;
    handleClickSourceText(searchQuery.value);
  };
  // 拦截 toast 函数
  const onToast = ({ msg, action }: { msg: string; action: string }) => {
    console.log(msg, action);
  };
  // 预览文件
  const handlePreviewUrl = (fileId: string | number) => {
    getEditUrl(fileId).then((res: any) => {
      if (res) {
        nextTick(() => {
          setTimeout(async () => {
            // eslint-disable-next-line import/no-named-as-default-member
            instance.value = OpenSDK.config({
              fileId: `document_fields_template_${Date.now()}`,
              url: res,
              mount: document.querySelector('#office'),
              mode: 'simple',
              onToast,
              wordOptions: {
                isShowDocMap: false, // 是否开启目录功能，默认开启
                isBestScale: true, // 打开文档时，默认以最佳比例显示
                isShowBottomStatusBar: false, // 是否展示底部状态栏
              },
              pdfOptions: {
                isShowComment: false, // 是否显示注解，默认显示
                isInSafeMode: false, // 是否处于安全模式（安全模式下不能划选文字，不能复制以及不能通过链接跳转），默认不是安全模式
                isBestScale: false, // 默认以最佳显示比例打开
                isShowBottomStatusBar: false, // 是否展示底部状态栏
                disBackScrollPos: true, // 是否禁用滚动还原
              },
            });
            await instance.value.ready();
            const app = instance.value.Application;
            window.app = app;
          }, 500);
        });
      }
    });
  };

  const handleClickSourceText = async (val: any, tab: string = 'word') => {
    if (!val || !val.highlight_text) return;

    if (tab !== tabIndex.value) {
      tabIndex.value = tab;
    }

    searchQuery.value = val;
    console.log(val);
    try {
      customLoading.value = {
        ...customLoading.value,
        loading: true,
      };
      const { highlight_text: highlightText, row_number: rowNumber, extend_lines: extendLiens = 0 } = val;
      const strList = highlightText.split('\n');

      if (instance.value && tab === 'word') {
        const app = instance.value.Application;
        if (app?.ActivePDF) {
          await app?.ActivePDF?.Find(strList).then(async () => {
            closeLoading();
          });
        } else if (app?.ActiveDocument) {
          wordFind(app, highlightText, highlightText.length, false);
        }
      } else if (tab === 'text') {
        highlightLineByNumber(parseInt(rowNumber, 10) - 1, parseInt(extendLiens, 10), highlightText);
      }

      closeLoading();
    } catch {
      closeLoading();
    }
  };
  function closeLoading() {
    customLoading.value = {
      ...customLoading.value,
      loading: false,
    };
  }

  const HIGHLIGHT_COLOR_INDEX_TEMP = 7; // 定义高亮颜色常量，提高可读性
  async function wordFind(
    app: any,
    text: string,
    length: number = 0,
    deep: boolean = false,
    recursionDepth: number = 0,
  ): Promise<void> {
    const MAX_RECURSION_DEPTH = length; // 设置最大递归深度，防止栈溢出

    if (recursionDepth > MAX_RECURSION_DEPTH) {
      // 递归深度超过限制，停止查找
      closeLoading();
      return;
    }

    if (!text) {
      // 文本已截空，未找到匹配项
      closeLoading();
      // 跳至纯文本匹配
      handleClickSourceText(searchQuery.value, 'text');
      return;
    }

    try {
      const activeDoc = app?.ActiveDocument;
      if (!activeDoc) throw new Error('文档对象不存在');

      const Find = await activeDoc.Find;
      if (!Find) throw new Error('查找功能不可用');

      const result = await Find.Execute(text, true);

      if (result?.length > 0) {
        const first = result.at(0);
        if (first && first.pos !== undefined && first.len !== undefined) {
          const { pos } = first;
          const len = deep && length ? length : first.len;
          const range = await activeDoc.Range(pos, pos + len);

          if (!deep) {
            await activeDoc.ActiveWindow.ScrollIntoView(range).then(() => {
              closeLoading();
            });
          } else {
            await range.SetRange(pos, pos + len);
            await activeDoc.ActiveWindow.ScrollIntoView(range).then(() => {
              activeDoc.ActiveWindow.Selection.Range.HighlightColorIndexTemp = HIGHLIGHT_COLOR_INDEX_TEMP;
              closeLoading();
            });
          }
        }
      } else {
        // 跳至纯文本匹配
        handleClickSourceText(searchQuery.value, 'text');
        // 截掉最后一个字符，递归调用
        // await wordFind(app, text.slice(0, -1), length, true, recursionDepth + 1);
      }
    } catch (error) {
      console.error('查找过程中发生错误:', error);
      closeLoading();
    }
  }

  const searchQuery = ref(null);
  // const matches = ref([]);
  // const currentMatchIndex = ref(-1);
  const originalContent = ref('');
  const highlightedContent = ref('');

  /**
   * 根据行号高亮特定行
   */
  const highlightLineByNumber = (lineNum: number, extendLiens: number, text: string) => {
    // 清除所有高亮
    highlightedContent.value = originalContent.value;

    const { length } = text.split('\n');
    const lines = originalContent.value.split('\n');
    const whileLen = extendLiens || length;
    if (lineNum < 0 || lineNum >= lines.length) {
      // 无效的行号
      return;
    }

    let index = 0;
    while (index < whileLen) {
      lines[lineNum + index] = `<span class="highlight" data-match-index="${index}">${lines[lineNum + index]}</span>`;
      index++;
    }

    highlightedContent.value = lines.join('\n');

    nextTick(() => {
      const nextEl = document.querySelector(`.highlight[data-match-index="0"]`);
      if (nextEl) {
        nextEl.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    });
  };
  const getFileContent = (fileId: string | number) => {
    fileContent(fileId).then((res: any) => {
      if (res) {
        originalContent.value = res;
        highlightedContent.value = res;
      }
    });
  };
  // 清除所有高亮
  // const resetSearch = () => {
  //   matches.value = [];
  //   currentMatchIndex.value = -1;
  //   highlightedContent.value = originalContent.value;
  // };
  // 查找所有匹配项并高亮
  // const findAllMatches = (query: string) => {
  //   if (!query.trim()) return;

  //   // 创建临时容器处理HTML
  //   const tempDiv = document.createElement('div');
  //   tempDiv.innerHTML = originalContent.value;

  //   walk(tempDiv, query);
  //   highlightedContent.value = tempDiv.innerHTML;
  // };
  // const processNode = (
  //   node: {
  //     nodeType: number;
  //     textContent: any;
  //     parentNode: { replaceChild: (arg0: DocumentFragment, arg1: any) => void };
  //   },
  //   query: string,
  // ) => {
  //   if (node.nodeType === Node.TEXT_NODE) {
  //     const text = node.textContent;
  //     const regex = new RegExp(query, 'gi');

  //     if (regex.test(text)) {
  //       const fragment = document.createDocumentFragment();
  //       let lastIndex = 0;

  //       text.replace(regex, (match: string, offset: number) => {
  //         // 添加匹配前的文本
  //         if (offset > lastIndex) {
  //           fragment.appendChild(document.createTextNode(text.substring(lastIndex, offset)));
  //         }

  //         // 添加高亮标记
  //         const span = document.createElement('span');
  //         span.className = 'highlight';
  //         span.dataset.matchIndex = `${matches.value.length}`;
  //         span.textContent = match;
  //         fragment.appendChild(span);

  //         matches.value.push(span);
  //         lastIndex = offset + match.length;
  //         return match;
  //       });

  //       // 添加剩余文本
  //       if (lastIndex < text.length) {
  //         fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
  //       }

  //       // 替换原始节点
  //       node.parentNode.replaceChild(fragment, node);
  //       return false; // 已处理，跳过子节点
  //     }
  //   }
  //   return true; // 继续处理子节点
  // };

  // 深度优先遍历所有节点
  // const walk = (node: ChildNode, query: string) => {
  //   if (processNode(node, query)) {
  //     let child = node.firstChild;
  //     while (child) {
  //       const { nextSibling } = child;
  //       walk(child, query);
  //       child = nextSibling;
  //     }
  //   }
  // };

  // 查找下一个匹配项
  // const findNext = (query: string) => {
  //   if (matches.value.length === 0) {
  //     if (query.trim()) {
  //       findAllMatches(query);
  //       nextTick(() => {
  //         const nextEl = document.querySelector(`.highlight[data-match-index="0"]`);
  //         if (nextEl) {
  //           nextEl.scrollIntoView({
  //             behavior: 'smooth',
  //             block: 'center',
  //           });
  //         }
  //       });
  //     }
  //     return;
  //   }

  //   // 移除当前高亮
  //   if (currentMatchIndex.value >= 0) {
  //     const currentEl = document.querySelector(`.highlight[data-match-index="${currentMatchIndex.value}"]`);
  //     if (currentEl) {
  //       currentEl.classList.remove('current-highlight');
  //     }
  //   }

  //   // 移动到下一个匹配项
  //   currentMatchIndex.value = (currentMatchIndex.value + 1) % matches.value.length;

  //   // 添加新的当前高亮
  //   const nextEl = document.querySelector(`.highlight[data-match-index="${currentMatchIndex.value}"]`);
  //   if (nextEl) {
  //     nextEl.classList.add('current-highlight');
  //     nextEl.scrollIntoView({
  //       behavior: 'smooth',
  //       block: 'center',
  //     });
  //   }
  // };

  /** 判断字符串是否为有效的 JSON */
  function isParsableJson(str: string): boolean {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }
  // 进度条定时器 ID，用于模拟加载动画
  let progressInterval: number | null = null;

  /**
   * 开始加载进度条，模拟文件解析过程。
   * 每 50ms 增加 1% 的进度，直到达到 90%
   */
  const startLoadingProgress = () => {
    percentage.value = 0;
    progressInterval = window.setInterval(() => {
      if (percentage.value < 90) {
        percentage.value += 1;
      }
    }, 50);
  };

  /**
   * 停止进度条并设置为完成状态（100%）
   */
  const stopLoadingProgress = () => {
    if (progressInterval !== null) {
      clearInterval(progressInterval);
      progressInterval = null;
      percentage.value = 100;
    }
  };

  return {
    highlightedContent,
    tabIndex,
    customLoading,
    instance,
    percentage,
    handlePreviewUrl,
    handleClickSourceText,
    isParsableJson,
    startLoadingProgress,
    stopLoadingProgress,
    closeLoading,
    tabClick,
    getFileContent,
  };
}
