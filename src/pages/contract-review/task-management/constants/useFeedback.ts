import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

import { reviewRuleExampleFeedback } from '@/api/modules/rule';
import { ExampleFeedback } from '@/api/types/rule';
import { MessageProps } from '@/api/types/task';
/** 提交反馈 */
interface ReviewItem {
  [key: string]: string;
}
export function useFeedback() {
  // 控制错误反馈弹窗是否显示的状态
  const showFeedback = ref(false);
  // 用于存储错误反馈弹窗中显示的审查项标题（例如规则名称）
  const relatedId = ref('');
  const relatedTitle = ref('');
  // 用于存储错误反馈弹窗中关联的原文内容列表，支持用户反馈时引用具体段落
  const relatedDocs = ref([]);
  const openFeedback = (item: MessageProps) => {
    if (!item.id) return;

    relatedId.value = item.reviewRuleId as string;
    relatedTitle.value = item.reviewRuleName;
    const sourceList = item.sourceText as unknown as ReviewItem[];
    const sourceMap = sourceList?.map((item: ReviewItem, i) => {
      console.log(item);
      return {
        label: `原文${i + 1}`,
        content: item.highlight_text,
        readonly: true,
      };
    });

    relatedDocs.value = sourceMap;
    showFeedback.value = true;
  };
  /**  提交反馈确认事件 */
  const handleFeedbackSubmit = (data: ExampleFeedback) => {
    reviewRuleExampleFeedback(data).then((res) => {
      if (res) {
        showFeedback.value = false;
        relatedId.value = '';
        relatedTitle.value = '';
        relatedDocs.value = [];
        MessagePlugin.success('提交成功');
      } else {
        MessagePlugin.error('提交失败');
      }
    });
  };

  return {
    showFeedback,
    relatedId,
    relatedTitle,
    relatedDocs,
    openFeedback,
    handleFeedbackSubmit,
  };
}
