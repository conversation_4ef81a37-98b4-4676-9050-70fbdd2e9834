import debounce from 'lodash/debounce';
import { computed, nextTick, ref } from 'vue';

import { listUsefulReviewRuleList } from '@/api/modules/rule';

export function useRule(formData: any, form: any) {
  // 存储下拉选择框的选项数据（规则列表）
  const selectOptions = ref([]);

  // 存储复选框组的选项数据（规则详情中的每一项）
  const checkboxOptions = ref([]);

  // 控制“全选”复选框的状态
  const isAllSelected = ref(false);

  // 存储当前规则列表的总条目数（用于显示规则总数）
  const checkboxTotal = ref(0);

  // 存储当前选中的规则对象（包含规则名称和子项）
  const activeRules = ref({});

  // 计算属性：判断复选框是否处于“部分选中”状态（用于控制“全选”组件 indeterminate 状态）
  const indeterminate = computed(() => {
    const checkedCount = formData.includedRules.length;
    return checkedCount > 0 && checkedCount < checkboxOptions.value.length;
  });

  /**
   * 搜索规则时触发的方法，使用 debounce 防抖处理输入搜索
   * @param keywords - 用户输入的搜索关键词
   */
  const searchRules = debounce((keywords: string) => {
    fetchTableData(keywords);
  }, 250);

  /**
   * 异步获取规则数据
   * 根据关键词搜索规则，并将返回结果映射成 { label, value } 格式供下拉选择器使用
   * 如果已有 reviewRuleListId，则保留当前选中规则
   * @param keywords - 搜索关键词
   */
  const fetchTableData = async (keywords: string, type: boolean = false) => {
    try {
      const params = {
        current: 1,
        size: 30,
        name: keywords,
      };
      const res = await listUsefulReviewRuleList(params);

      // 将接口返回的数据格式化为 { label, value, ruleVos } 结构，便于后续使用
      const records = res.map((item: any) => ({
        label: item.name,
        value: item.id,
        ruleVos: item.ruleVos,
      }));

      // 如果当前已有选中的规则列表 ID，将其重新加入选项中（防止被过滤掉）
      if (formData.reviewRuleListId) {
        records.push(activeRules.value);
      }

      selectOptions.value = records || [];
      const preRuleId = localStorage.getItem('pre_rule_id');
      if (records.length && type) {
        const temp = records.find((item) => item.value === preRuleId);
        formData.reviewRuleListId = temp?.value || records[0].value;
        handleRuleChange(temp?.value || records[0].value);
      }
    } catch (error) {
      // 错误处理，提示用户具体错误信息
      console.error(error);
    }
  };
  /**
   * 下拉选择规则时触发的事件处理函数
   * 根据选中的规则 ID 获取对应的规则列表，并更新复选框选项和选中状态
   * @param value - 选中的规则 ID
   */
  const handleRuleChange = (value: any) => {
    if (!value) return;

    // 查找选中的规则对象
    const item = selectOptions.value.find((item: any) => item.value === value);
    if (item) {
      activeRules.value = item;
      const ruleVos = item.ruleVos || [];

      // 更新规则数量、全选状态、表单数据、复选框选项
      checkboxTotal.value = ruleVos.length;
      isAllSelected.value = true;
      formData.reviewRuleListName = item.label;
      formData.includedRules = ruleVos.map((rule: any) => rule.id);
      checkboxOptions.value = ruleVos.map((rule: any) => ({
        label: rule.name,
        value: rule.id,
      }));
    } else {
      // 如果未找到对应规则，重置相关状态
      handleRuleClear();
    }

    // 清除表单校验提示（在 DOM 更新后执行）
    nextTick(() => {
      form.value?.validate({
        includedRules: [{ required: true, message: '请选择至少一个审查规则项', trigger: 'blur' }],
      });
    });
  };

  /**
   * 清除规则选择时触发的事件处理函数
   * 重置所有与规则相关的状态
   */
  const handleRuleClear = () => {
    activeRules.value = {};
    checkboxTotal.value = 0;
    isAllSelected.value = false;
    checkboxOptions.value = [];
    formData.includedRules = [];
    formData.reviewRuleListName = '';
    form.value?.validate({
      includedRules: [{ required: true, message: '请选择至少一个审查规则项', trigger: 'blur' }],
    });
  };

  /**
   * 全选复选框点击事件
   * 控制是否选中所有规则项
   * @param checked - 是否选中
   */
  const handleSelectAll = (checked: boolean) => {
    isAllSelected.value = checked;
    if (checked) {
      formData.includedRules = checkboxOptions.value.map((item: any) => item.value);
    } else {
      formData.includedRules = [];
    }
  };

  /**
   * 复选框组变更事件
   * 当用户勾选部分规则时更新“全选”状态
   * @param value - 当前选中的规则值数组
   */
  const handleCheckboxChange = (value: any[]) => {
    // 判断是否全部选中
    isAllSelected.value = value.length === checkboxOptions.value.length;
  };

  return {
    handleCheckboxChange,
    handleSelectAll,
    handleRuleClear,
    handleRuleChange,
    fetchTableData,
    searchRules,
    selectOptions,
    checkboxOptions,
    isAllSelected,
    checkboxTotal,
    activeRules,
    indeterminate,
  };
}
