<template>
  <div v-loading="loading" class="task-management">
    <t-card class="list-card-container">
      <t-row justify="space-between" style="margin-bottom: 16px">
        <t-col>
          <div class="route-title">
            {{ route.meta.title }}
          </div>
        </t-col>
        <t-col>
          <t-button v-permission="menuBlockList.includes('ADD')" theme="primary" @click="onCreateTask">
            新建任务
          </t-button>
        </t-col>
      </t-row>

      <t-table
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        table-layout="auto"
        row-key="id"
        @page-change="onPageChange"
      >
        <template #fileName="{ row }">
          <t-link theme="primary" hover="color" @click="onViewTaskInfo(row)">{{ row.fileName }}</t-link>
        </template>
        <template #totalDuration="{ row }">
          <div v-if="afootList.includes(row.status)">
            <time-display :created-time="row.createdTime" />
          </div>
          <div v-else>
            {{ getTotalDuration(row.totalDuration) }}
          </div>
        </template>
        <template #status="{ row }">
          <t-button size="small" :loading="row.loading" :theme="getStatusTheme(row.status)">
            {{ getStatusText(row.status) }}
            <template v-if="afootList.includes(row.status)">
              {{ row?.resultsVoList?.length || 0 }}/{{ getRuleCount(row?.includedRules) }}
            </template>
          </t-button>
        </template>
        <template #op="{ row }">
          <t-space>
            <t-link theme="primary" hover="color" @click="onViewTaskInfo(row)">查看详情</t-link>
            <t-link theme="primary" hover="color" @click="onViewDetail(row)">任务信息</t-link>
            <template v-if="afootList.includes(row.status)">
              <t-popconfirm theme="danger" :content="`确定要终止任务 ${row.fileName} 吗？`" @confirm="onTerminate(row)">
                <t-link v-permission="menuBlockList.includes('TERMINATED')" theme="warning" hover="color">
                  终止任务
                </t-link>
              </t-popconfirm>
            </template>
            <template v-if="row.status === STATUS.COMPLETED || row.status === STATUS.TERMINATED">
              <t-link
                v-permission="menuBlockList.includes('EXPORT')"
                theme="primary"
                hover="color"
                @click="onExport(row)"
              >
                导出结果
              </t-link>
            </template>
            <template v-if="row.status === STATUS.FAILED || row.status === STATUS.ANALYSING">
              <t-link
                v-permission="menuBlockList.includes('RELOAD')"
                theme="danger"
                hover="color"
                @click="handleReload(row)"
              >
                重试
              </t-link>
            </template>
            <template v-if="!afootList.includes(row.status)">
              <t-popconfirm theme="danger" :content="`确定要删除任务 ${row.fileName} 吗？`" @confirm="onDelete(row)">
                <t-link v-permission="menuBlockList.includes('DELETE')" theme="danger" hover="color">删除</t-link>
              </t-popconfirm>
            </template>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 任务详情弹窗 -->
    <t-dialog v-model:visible="taskDetailVisible" header="任务详情" :footer="false" width="600px">
      <t-form colon label-width="120px">
        <t-form-item label="任务ID">{{ currentTask.id }}</t-form-item>
        <t-form-item label="合同名称">{{ currentTask.fileName }}</t-form-item>
        <t-form-item label="创建时间">{{ currentTask.createdTime }}</t-form-item>
        <t-form-item label="完成时间">{{ currentTask.updatedTime }}</t-form-item>
        <t-form-item label="任务状态">
          <t-tag :theme="getStatusTheme(currentTask.status)">
            {{ getStatusText(currentTask.status) }}
          </t-tag>
        </t-form-item>
        <t-form-item label="审查模式">{{ REVIEW_MODE_ENUM[currentTask.reviewMode] }}</t-form-item>
        <t-form-item label="审查范围">
          {{ currentTask.reviewPageRange === 'ALL' ? '整篇文档' : currentTask.reviewPageRange }}
        </t-form-item>
        <t-form-item label="规则数">{{ getRuleCount(currentTask.includedRules) }}</t-form-item>
        <t-form-item label="任务总时长">{{ getTotalDuration(currentTask.totalDuration) }}</t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'TaskManagement',
};
</script>
<script lang="ts" setup>
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onActivated, onDeactivated, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  exportExcel,
  hasReviewingTask,
  queryStatus,
  retryById,
  taskDeleteById,
  taskPageByVo,
  taskTerminatedById,
} from '@/api/modules/task';
import { TaskItem } from '@/api/types/task';
import TimeDisplay from '@/components/time-display';

import { REVIEW_MODE_ENUM, STATUS, STATUS_TEXT_MAP, STATUS_THEME_MAP, TABLE_COLUMNS } from './constants';

// 从 constants 中导入定义好的表格列配置
const columns = TABLE_COLUMNS;
// 状态主题映射表：将任务状态字符串（如 'ONGOING'）映射为 UI 主题颜色
const statusThemeMap: Record<string, string> = STATUS_THEME_MAP;
// 状态文本映射表：将任务状态码转换为用户可见的文本描述
const statusTextMap: Record<string, string> = STATUS_TEXT_MAP;

const router = useRouter();
const route = useRoute();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

const loading = ref(false); // 控制全局加载状态，配合 v-loading 使用显示加载动画
const taskDetailVisible = ref(false); // 控制任务详情弹窗的显示与隐藏
const tableData = ref<TaskItem[]>([]); // 表格数据源，存储当前页的任务列表数据
const currentTask = ref<TaskItem>({} as TaskItem); // 当前选中的任务对象，用于弹窗展示详细信息
// 进行中
const afootList: string[] = [STATUS.ONGOING, STATUS.REVIEWING, STATUS.ANALYSING, STATUS.ANALYZED];

// 分页器配置对象，使用 reactive 实现响应式更新
const pagination = reactive({
  total: 0,
  pageSize: 10,
  current: 1,
});

// 计算属性，用于获取进行中的任务列表
const getListIds = computed(() => {
  return tableData.value
    .filter((item) => {
      return afootList.includes(item.status);
    })
    .map((item) => item.id);
});

/**
 * 根据任务状态获取对应的主题颜色
 * @param status - 任务状态字符串（如 'ONGOING', 'COMPLETED'）
 * @returns 对应的颜色标识符或默认值 'default'
 */
const getStatusTheme = (status: string): any => {
  return statusThemeMap[status] || 'default';
};

/**
 * 根据任务状态返回对应的显示文本
 * @param status - 任务状态字符串
 * @returns 对应的状态描述文本，若未定义则返回原始状态值
 */
const getStatusText = (status: string): string => {
  return statusTextMap[status] || status;
};

/**
 * 将秒数格式化为小时、分钟、秒的组合字符串
 * @param duration - 持续时间（单位：秒），可以是number或string类型
 * @returns 格式化后的时间字符串，例如 "1小时30分钟20秒"
 */
const getTotalDuration = (duration: number | string): string => {
  const durationNumber = typeof duration === 'string' ? parseInt(duration, 10) : duration;

  if (!durationNumber || durationNumber <= 0) return '0秒';

  const hours = Math.floor(durationNumber / 3600);
  const minutes = Math.floor((durationNumber % 3600) / 60);
  const seconds = durationNumber % 60;

  let result = '';
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0) {
    result += `${minutes}分钟`;
  }
  if (seconds > 0) {
    result += `${seconds}秒`;
  }

  return result;
};

/**
 * 计算规则字符串中包含的规则数量
 * @param rule - 规则字符串，形式为逗号分隔（如 "RULE_1,RULE_2"）
 * @returns 规则数量，若为空则返回 0
 */
const getRuleCount = (rule: string) => {
  if (!rule) return 0;
  return rule.split(',').length;
};

/**
 * 执行搜索操作，重新加载表格数据
 * 设置 loading 状态为 true，调用 fetchTableData 加载数据，
 * 最终将 loading 状态设为 false，不管是否成功。
 */
const onSearch = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    await fetchTableData();
  } finally {
    loading.value = false;
  }
};
/**
 * 跳转到任务创建页面
 */
const onCreateTask = () => {
  hasReviewingTask().then((res) => {
    if (res) router.push('/contract-review/create-task');
  });
};

/**
 * 处理分页信息变更
 * 当用户切换页码或修改每页显示条数时，更新分页器的状态并重新加载数据。
 * @param pageInfo - 包含当前页码和可选的每页大小的对象
 */
const onPageChange = (pageInfo: { current: number; pageSize?: number }) => {
  pagination.current = pageInfo.current;
  if (pageInfo.pageSize) pagination.pageSize = pageInfo.pageSize;
  onSearch();
};

/**
 * 显示任务详情弹窗
 * 将当前任务对象赋值给 currentTask，并打开任务详情对话框。
 * @param row - 被点击的任务数据对象
 */
const onViewDetail = (row: TaskItem) => {
  // 将当前任务赋值给 currentTask 用于弹窗展示
  currentTask.value = row;
  // 显示任务详情对话框
  taskDetailVisible.value = true;
};

const onExport = (row: TaskItem) => {
  const fileName = `${row?.fileName?.split('.')?.slice(0, -1)?.join('.')} 审查结果`;
  exportExcel(row.id, fileName).then(() => {
    MessagePlugin.success(`导出 ${fileName} 成功`);
  });
};

/**
 * 处理任务重试操作 当用户点击"重试"按钮时，调用此函数来重新连接消息流并尝试继续处理任务。
 * @param row - 当前任务对象，包含任务的详细信息（如 ID、状态、结果等）
 */
const handleReload = (row: TaskItem) => {
  // 设置当前任务为加载状态，显示 loading 动画
  hasReviewingTask().then((res) => {
    if (res) {
      row.loading = true;

      retryById(row.id).then((res: any) => {
        if (res) onSearch();
      });
    }
  });
};

/**
 * 终止指定的任务
 * 当用户点击"终止任务"按钮时调用此函数。
 * 向后端发送终止任务请求，成功后刷新任务列表。
 * @param row - 要终止的任务对象
 */
const onTerminate = (row: TaskItem) => {
  taskTerminatedById(row.id).then((res) => {
    if (!res) {
      MessagePlugin.error('终止任务失败');
      return;
    }
    MessagePlugin.success('终止任务成功');
    onSearch(); // 刷新任务列表
  });
};

/**
 * 删除指定的任务
 * 当用户点击"删除"按钮时调用此函数。
 * 向后端发送删除任务请求，成功后刷新任务列表。
 * @param row - 要删除的任务对象
 */
const onDelete = (row: TaskItem) => {
  taskDeleteById(row.id).then((res) => {
    if (!res) {
      MessagePlugin.error('删除失败');
      return;
    }
    MessagePlugin.success('删除成功');
    onSearch(); // 刷新任务列表
  });
};

/**
 * 跳转到任务详情页面
 * @param row - 当前任务对象
 */
const onViewTaskInfo = (row: TaskItem) => {
  router.push(`/contract-review/create-task?taskId=${row.id}`);
};

/**
 * 从服务器获取当前分页的任务列表数据
 */
const fetchTableData = async () => {
  try {
    const { current, pageSize } = pagination;
    const res = await taskPageByVo({ current, size: pageSize });
    tableData.value = res.records || [];
    pagination.total = Number(res.total) || 0;
    if (getListIds.value.length) {
      getTimeoutList();
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  }
};
// 轮询获取任务列表  更新进行中的任务状态
const timer = ref<NodeJS.Timeout | null>(null);
// 查询数据  更新状态
const getTimeoutList = () => {
  queryStatus(getListIds.value).then((res: any) => {
    res?.forEach((item: any) => {
      const index = tableData.value.findIndex((record) => record.id === item.id);
      if (index !== -1) {
        tableData.value[index] = item;
      }
    });
    if (getListIds.value.length) {
      timer.value = setTimeout(() => {
        getTimeoutList();
      }, 2000);
    } else {
      clearTimeout(timer.value);
    }
  });
};

/**
 * 页面激活时触发数据加载
 * 当组件被激活（如切换标签页回来）时自动调用 onSearch 方法刷新数据。
 */
onMounted(() => {
  onSearch();
});
onActivated(() => {
  onSearch();
});
onDeactivated(() => {
  clearTimeout(timer.value);
});
</script>

<style lang="less" scoped>
.task-management {
  padding: 0;
  .list-card-container {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);

    :deep(.t-card__body) {
      padding: 0;
    }
  }
  .route-title {
    height: 22px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
}
</style>
