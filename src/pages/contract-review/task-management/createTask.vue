<template>
  <div class="create-task">
    <t-card v-if="showReview === 0" v-loading="true"></t-card>
    <t-card v-if="showReview === 1" v-loading="loading">
      <div class="scroll-content">
        <div class="route-title">审查任务管理>{{ route.meta.title }}</div>

        <t-form ref="form" class="form-container" label-align="top" :data="formData" :rules="rules">
          <t-row justify="space-between" :gutter="40" style="margin-bottom: 16px">
            <t-col :span="6">
              <!-- 上传文件 -->
              <t-form-item name="fileId">
                <template #label>
                  <div class="form-title">
                    上传文件&nbsp;
                    <!-- <t-tooltip theme="primary" :duration="3000" content="这是Tooltip内容">
                    <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
                  </t-tooltip> -->
                  </div>
                </template>
                <t-upload
                  ref="uploadRef"
                  class="upload-file"
                  :action="uploadUrl"
                  :headers="headers"
                  accept=".pdf,.doc,.docx"
                  :format-response="formatResponse"
                  :mock-progress-duration="100"
                  :multiple="false"
                  draggable
                  tips="点击上传按钮上传文件，支持pdf/doc/docx格式，限制文件大小200MB内"
                  :before-upload="beforeUpload"
                  @success="(context: any) => onUploadSuccess(context)"
                  @remove="(context: any) => onUploadRemove(context)"
                  @fail="onUploadFail"
                  @click="handleUploadClick"
                >
                </t-upload>
              </t-form-item>
              <!-- 审查模式 -->
              <t-form-item name="reviewMode">
                <template #label>
                  <div class="form-title">
                    审查模式&nbsp;
                    <!-- <t-tooltip theme="primary" :duration="3000" content="这是Tooltip内容">
                    <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
                  </t-tooltip> -->
                  </div>
                </template>
                <div class="radio-group">
                  <div
                    :class="['mode-item', { active: formData.reviewMode === REVIEW_MODE.HIGH_SPEED }]"
                    @click="formData.reviewMode = REVIEW_MODE.HIGH_SPEED"
                  >
                    <div class="mode-title">{{ REVIEW_MODE_ENUM.HIGH_SPEED }}</div>
                    <div class="mode-desc">采用私有化模型，数据安全有保障。可直接上传文件。</div>
                  </div>
                  <div
                    :class="['mode-item', { active: formData.reviewMode === REVIEW_MODE.HIGH_QUALITY }]"
                    @click="formData.reviewMode = REVIEW_MODE.HIGH_QUALITY"
                  >
                    <div class="mode-title">{{ REVIEW_MODE_ENUM.HIGH_QUALITY }}</div>
                    <div class="mode-desc">采用云端大参数规模模型，精度相对更高；文件建议脱敏后上传。</div>
                  </div>
                </div>
              </t-form-item>

              <!-- 审查范围 -->
              <t-form-item name="reviewPageRange">
                <template #label>
                  <div class="form-title">
                    审查范围&nbsp;
                    <t-tooltip
                      theme="primary"
                      :duration="3000"
                      content="选取合适的审查范围将有助于AI聚焦在关键内容，提升审查效果及速度"
                    >
                      <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
                    </t-tooltip>
                  </div>
                </template>
                <div class="radio-group-2">
                  <t-radio-group v-model="formData.reviewPageRange">
                    <t-radio value="ALL">整篇文档</t-radio>
                    <t-radio value="custom">指定页数范围</t-radio>
                  </t-radio-group>
                  <div v-if="formData.reviewPageRange === 'custom'" class="page-range">
                    <t-input-number v-model="formData.startPage" theme="column" :min="1" />
                    <span class="separator">-</span>
                    <t-input-number v-model="formData.endPage" theme="column" :min="1" :max="1000" />
                  </div>
                </div>
              </t-form-item>
            </t-col>

            <t-col :span="6">
              <!-- 审查规则 -->
              <t-form-item class="rules" name="includedRules">
                <template #label>
                  <div class="form-title">
                    审查规则&nbsp;
                    <!-- <t-tooltip theme="primary" :duration="3000" content="这是Tooltip内容">
                    <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
                  </t-tooltip> -->
                  </div>
                </template>
                <t-select
                  v-model="formData.reviewRuleListId"
                  style="width: 300px"
                  placeholder="请选择审查规则"
                  filterable
                  clearable
                  :options="selectOptions"
                  @search="searchRules"
                  @change="handleRuleChange"
                  @clear="handleRuleClear"
                >
                </t-select>
                <div v-if="checkboxOptions.length" class="rule-list">
                  <div class="rule-group">
                    <t-checkbox
                      label="全选"
                      :indeterminate="indeterminate"
                      :checked="isAllSelected"
                      @change="handleSelectAll"
                    />
                    <span class="rule-count">({{ checkboxTotal }})</span>
                  </div>
                  <t-checkbox-group v-model="formData.includedRules" style="width: 100%" @change="handleCheckboxChange">
                    <div class="rules-container">
                      <div v-for="(item, index) in checkboxOptions" :key="index" class="rule-row">
                        <t-checkbox :value="item.value" :label="item.label"></t-checkbox>
                      </div>
                    </div>
                  </t-checkbox-group>
                </div>
              </t-form-item>
            </t-col>
          </t-row>
          <!-- 操作按钮 -->
        </t-form>
      </div>

      <t-form-item class="operation-btns">
        <t-space :align="'center'">
          <t-button v-permission="menuBlockList.includes('OPEN_TASK')" theme="primary" @click="onSubmit">
            开始审查
          </t-button>
          <t-button theme="default" @click="onCancel">取消</t-button>
        </t-space>
      </t-form-item>
    </t-card>
    <components-review v-if="showReview === 2" v-model:children-data="childrenData"></components-review>
  </div>
</template>
<script lang="ts">
export default {
  name: 'CreateTaskManagement',
};
</script>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import { FormRule, MessagePlugin, UploadFile } from 'tdesign-vue-next';
import { onActivated, onDeactivated, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { hasReviewingTask } from '@/api/modules/task';
import { SaveSchema } from '@/api/types/task';
import { useUserStore } from '@/store';

import ComponentsReview from './components/ReviewTask.vue';
import { REVIEW_MODE, REVIEW_MODE_ENUM } from './constants';
import { useRule } from './constants/useRule';
// 引入 Vue Router 的 useRouter 和 useRoute 方法，用于处理路由跳转和获取当前路由信息
const router = useRouter();
const route = useRoute();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];
// 获取表单组件实例的引用，用于调用表单校验等方法
const form = ref();

// 控制页面展示状态：0-加载中；1-显示表单；2-显示审查结果
const showReview = ref(0);

// 页面加载状态控制，用于在提交或异步操作时显示 loading 效果
const loading = ref(false);

const formData = reactive<SaveSchema>({
  fileId: '',
  fileName: '',
  includedRules: [],
  reviewRuleListName: '',
  reviewRuleListId: '',
  reviewMode: REVIEW_MODE.HIGH_SPEED,
  reviewPageRange: 'ALL',
  startPage: 1,
  endPage: 200,
  type: 'REVIEW',
});

const rules: Record<string, FormRule[]> = {
  reviewMode: [{ required: true, message: '请选择审查模式', trigger: 'blur' }],
  reviewPageRange: [{ required: true, message: '请选择审查范围', trigger: 'blur' }],
  fileId: [{ required: true, message: '请上传文件', trigger: 'blur' }],
  includedRules: [{ required: true, message: '请选择至少一个审查规则项', trigger: 'blur' }],
};

// 审查规则use
const {
  handleCheckboxChange,
  handleSelectAll,
  handleRuleClear,
  handleRuleChange,
  fetchTableData,
  searchRules,
  selectOptions,
  checkboxOptions,
  isAllSelected,
  checkboxTotal,
  indeterminate,
} = useRule(formData, form);

const uploadUrl = `${import.meta.env.VITE_API_URL_PREFIX}/taskFiles/upload`;
// 携带Token信息
const { token } = storeToRefs(useUserStore());
const headers = {
  [import.meta.env.VITE_TOKEN_NAME]: token.value,
};
// 限制文件大小为 200MB（单位：字节）
const MAX_FILE_SIZE = 200 * 1024 * 1024;
const beforeUpload = (file: UploadFile) => {
  if (file.size > MAX_FILE_SIZE) {
    // 文件超出大小限制，阻止上传
    MessagePlugin.error(`文件大小超出限制，最大支持 ${MAX_FILE_SIZE / 1024 / 1024}MB`);
    return false;
  }
  return true; // 允许上传
};

// 格式化上传响应数据
const formatResponse = (res: any) => {
  if (res?.resultCode === 0) {
    return { id: res.data };
  }
  return { error: res?.errMessage };
};

const fileValidate = () => {
  form.value?.validate({
    fileId: [{ required: true, message: '请上传文件', trigger: 'blur' }],
  });
};
// 文件上传成功回调
const onUploadSuccess = ({ file }: { file: any }) => {
  // 设置文件 ID 和文件名到表单数据中
  formData.fileId = file.response.id;
  formData.fileName = file.name;
  fileValidate();
};

// 文件移除回调
const onUploadRemove = ({ file }: { file: any }) => {
  // 清空已上传的文件信息
  formData.fileId = '';
  formData.fileName = '';
  fileValidate();
};

// 文件上传失败回调
const onUploadFail = ({ file }: { file: any }) => {
  // 清空文件信息并提示错误
  formData.fileId = '';
  formData.fileName = '';
  fileValidate();
  MessagePlugin.error(`文件 ${file.name} 上传失败`);
};

/**
 * 提交表单处理函数
 * 验证表单数据，若验证通过，则构造请求参数并调用 taskSave 接口提交任务创建请求
 */
let childrenData = reactive<any>({});
const onSubmit = async () => {
  hasReviewingTask().then(async (res) => {
    if (res) {
      const valid = await form.value?.validate();
      if (valid === true) {
        // 构造提交数据，将 includedRules 从 string[] 转换为逗号分隔的字符串以适配接口要求
        const submitData = {
          ...formData,
          modelId: 4,
          includedRules: formData.includedRules.join(','),
          reviewPageRange: formData.reviewPageRange === 'ALL' ? 'ALL' : `${formData.startPage},${formData.endPage}`,
        };
        childrenData = submitData;
        showReview.value = 2;
        localStorage.setItem('pre_rule_id', valid.reviewRuleListId);
      }
    }
  });
};
/**
 * 取消按钮点击事件，返回任务管理页面
 */
const onCancel = () => {
  router.push('/contract-review/task-management');
};

const uploadRef = ref(); // 上传文件实例
const handleUploadClick = (e: { target: any }) => {
  // 触发上传  只有是upload大框的时候才触发
  if (e.target.className.includes('t-upload__dragger')) {
    uploadRef.value.triggerUpload();
  }
};

onActivated(async () => {
  const id = route.query.taskId as string;
  if (id) {
    showReview.value = 2;
  } else {
    showReview.value = 1;
    fetchTableData('', true);
  }
});
onDeactivated(() => {
  showReview.value = 0;
});
</script>

<style lang="less" scoped>
@import './less/create-task.less';
</style>
