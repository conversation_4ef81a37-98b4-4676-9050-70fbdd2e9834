.review {
  height: 100%;
  min-width: 900px;
  :deep(.t-card) {
    height: 100%;
    .t-card__body {
      height: 100%;
      overflow: hidden;
      padding: 10px 10px 0;
    }
  }
  .container-base {
    height: 100%;
    .t-col {
      height: 100%;
    }
    .t-col:not(last-child) {
      border-right: 1px solid #ebebeb;
    }
  }
  .col-title,
  .col-title-text {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: calc(100% + 20px);
      height: 1px;
      background-color: #ebebeb;
    }
    .col-title-text-title {
      display: flex;
      align-items: center;
      color: var(--td-brand-color);
      cursor: pointer;
      .t-icon {
        font-size: 20px;
      }
    }
    .col-title-text-subtitle {
      margin-right: 20px;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: calc(100% - 240px);
      color: var(--td-brand-color);
    }
    .col-title-text-line {
      margin-left: auto;
    }
    &.col-title-text2 {
      &::after {
        left: -20px;
      }
    }
    .col-title-text-progress {
      margin-left: auto;
      color: var(--td-error-color);
      .t-icon {
        font-size: 20px;
        margin: 0 10px;
        color: var(--td-brand-color);
      }
    }
    .col-title-text-progress-bar {
      display: flex;
      align-items: center;
      color: var(--td-brand-color);
      margin-left: auto;
      .col-title-text-progress-bar-export {
        margin: 0 15px;
        cursor: pointer;
        padding: 4px 15px;
        color: var(--td-text-color-anti);
        background-color: var(--td-brand-color);
        border-color: var(--td-brand-color);
        border-radius: 15px;
        user-select: none;
      }
    }
  }
  .col-l-body {
    height: calc(100% - 50px);
    overflow: hidden;
    position: relative;
    .percentage {
      width: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      flex-direction: column;
      .percentage-img {
        text-align: center;
        .t-icon {
          font-size: 80px;
          color: var(--td-brand-color);
        }
      }
      .percentage-text {
        text-align: center;
        margin: 20px 0;
        font-size: 14px;
        color: var(--td-brand-color);
      }
      .percentage-error-title {
        margin-top: 10px;
        font-weight: bold;
        font-size: 18px;
        margin-bottom: 8px;
        color: #dc3545;
      }
      .percentage-error-p {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
    .tips-1 {
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: var(--td-brand-color);
    }
    .tips-2 {
      width: 100%;
      text-align: center;
      position: absolute;
      left: 0;
      bottom: 10px;
      color: var(--td-brand-color);
    }
    .open-sdk-iframe{
      width: 100%;
      height: calc(100% - 10px);
      iframe {
        width: 100%;
        height: 100%;
      }
    }
    .open-sdk-iframe2 {
      width: 100%;
      height: calc(100vh - 150px);
      overflow: hidden;
      overflow-y: auto;
      white-space: pre-line;
    }
  }
  .col-l-body-content {
    padding-top: 10px;
    height: 100%;
    overflow: hidden;
    .tips {
      line-height: 24px;
      text-align: center;
      background-color: rgb(242, 242, 242);
      color: var(--td-brand-color);
      white-space: nowrap;
    }
    .radio-group {
      width: 100%;
      padding: 15px 0;
      border-bottom: 1px solid #ebebeb;
    }
    .result-box-overflow {
      height: calc(100vh - 330px);
      overflow: hidden;
      overflow-y: auto;
    }

    .result-box {
      border-bottom: 1px solid #ebebeb;
      border-radius: 5px;
      padding: 20px 0px 20px 10px;
      margin-bottom: 10px;
      position: relative;
      .feedback-btn {
        position: absolute;
        right: 0px;
        bottom: 2px;
        display: none;
      }
      &:hover {
        .feedback-btn {
          display: block;
        }
      }
    }

    .result-status {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .text {
        margin-left: 10px;
        font-weight: bold;
        display: flex;
        align-items: center;
        span {
          flex: 1;
        }
        svg {
          min-width: 20px;
          margin-left: 4px;
        }
      }
    }

    .status-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 5px;
    }

    .status-text {
      margin-right: 10px;
    }

    .risk-level {
      margin-left: auto;
      font-size: 14px;
      font-weight: bold;
      min-width: 62px;
      padding-left: 10px;
      &.level2 {
        color: #ffc107;
      }
      &.level3 {
        color: #dc3545;
      }
    }

    .result-description {
      padding: 10px 20px;
      background-color: rgb(242, 242, 242);
      border-radius: 4px;
      margin: 10px 0;
      color: #495057;
      line-height: 1.5;
    }

    .original-text {
      margin-top: 5px;
    }

    .original-text a {
      color: #fd7e14;
      text-decoration: underline;
      &.primary {
        color: var(--td-brand-color);
      }
    }
  }
}