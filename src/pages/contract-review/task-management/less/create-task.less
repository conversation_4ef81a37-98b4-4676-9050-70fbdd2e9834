.create-task {
  height: calc(100vh - 88px);
  overflow: hidden;
  min-width: 900px;
  :deep(.t-card) {
    height: 100%;
  }
  :deep(.t-card__body) {
    display:flex;
    flex-direction: column;
    height: 100%;
  }

  .scroll-content {
    height: 1;
    overflow: auto;
  }
  
  .route-title,
  .form-title {
    height: 22px;
    font-size: 12px;
    color: var(--td-brand-color-active);
    font-weight: bold;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
  .form-container {
    padding: 20px;
    border: 1px solid var(--td-component-border);
    border-radius: 6px;
    :deep(.t-form__label--required:not(.t-form__label--required-right) label::before) {
      display: none;
    }
  }
  :deep(.t-upload) {
    width: 100%;
    .t-upload__dragger {
      width: 100%;
    }
  }
  .radio-group {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
  }
  .mode-item {
    flex: 1;
    padding: 16px;
    border: 1px solid var(--td-component-border);
    border-radius: var(--td-radius-medium);
    cursor: pointer;
    &.active {
      border: 1px solid var(--td-brand-color-active);
    }

    .mode-title {
      font-weight: bold;
      margin-bottom: 8px;
      color: var(--td-brand-color-active);
    }

    .mode-desc {
      color: var(--td-text-color-secondary);
      font-size: 12px;
      line-height: 20px;
    }
  }
  .radio-group-2 {
    display: flex;
    height: 40px;
    gap: 10px;
    align-items: center;
  }
  .rules {
    :deep(.t-form__controls-content) {
      display: block;
    }
  }
  .upload-drag-area {
    padding: 40px 0;
    text-align: center;
    border: 1px dashed var(--td-component-border);
    border-radius: var(--td-radius-medium);

    .tips {
      color: var(--td-text-color-secondary);
      font-size: 13px;
      margin-top: 8px;
    }
  }

  .rule-list {
    width: 100%;
    margin-top: 16px;
    padding: 16px;

    .rule-group {
      margin-bottom: 16px;
      .rule-count {
        color: var(--td-text-color-secondary);
        margin-left: 4px;
      }
    }

    .rules-container {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
    }
  }

  .page-range {
    display: flex;
    align-items: center;

    .separator {
      margin: 0 8px;
    }
  }

  .operation-btns {
    margin-top: 32px;
    text-align: center;
    :deep(.t-form__controls-content) {
      display: flex;
      justify-content: center;
    }
  }

  .upload-progress {
    padding: 20px;

    .progress-info {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .file-name {
        margin-left: 8px;
      }
    }

    .progress-status {
      margin-top: 8px;
      text-align: center;
      color: var(--td-text-color-secondary);
    }
  }
}