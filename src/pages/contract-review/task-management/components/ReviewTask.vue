<template>
  <div v-loading="customLoading" class="review">
    <t-card>
      <t-row class="container-base" justify="space-between" :gutter="40">
        <t-col :span="6">
          <div class="col-title">
            <div class="col-title-text">
              <div v-if="currentTask.id || percentage === 100" class="col-title-text-title" @click="handleBack">
                <chevron-left-s-icon />
                返回&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;
              </div>
              <div class="col-title-text-subtitle">{{ currentTask.fileName || childrenData.fileName }}</div>
              <div v-if="!errorCode && (currentTask.id || percentage === 100)" class="col-title-text-line">
                <t-button
                  :theme="tabIndex === 'word' ? 'primary' : 'default'"
                  size="small"
                  variant="outline"
                  @click="tabClick('word')"
                >
                  原文档
                </t-button>
                <t-button
                  :theme="tabIndex === 'text' ? 'primary' : 'default'"
                  size="small"
                  variant="outline"
                  @click="tabClick('text')"
                >
                  纯文字
                </t-button>
              </div>
            </div>
          </div>
          <div class="col-l-body">
            <div v-if="!!errorCode" class="percentage">
              <div class="percentage-img"><catalog-icon /></div>
              <div class="percentage-error-title">{{ errMessage }}</div>
              <div class="percentage-error-p">任务出现了一些问题，您可以尝试重试或联系管理员提供支持</div>
              <div class="percentage-error-p">错误代码（{{ errorCode }}）</div>
              <t-button v-if="id" style="width: 90px" theme="primary" variant="outline" @click="reloadPageData">
                重试
              </t-button>
            </div>
            <div v-else-if="percentage > 0 && percentage < 100" class="percentage">
              <div class="percentage-img"><catalog-icon /></div>
              <div class="percentage-text">文件解析中…</div>
              <t-progress
                style="width: 100%"
                theme="plump"
                :color="{ from: '#00A870', to: '#0052D9' }"
                :percentage="percentage"
                :status="'active'"
              />
            </div>
            <template v-else>
              <div v-show="tabIndex === 'word'" id="office" class="open-sdk-iframe"></div>
              <div v-show="tabIndex === 'text'" class="open-sdk-iframe2" v-html="highlightedContent"></div>
            </template>
          </div>
        </t-col>
        <t-col :span="6">
          <div class="col-title">
            <div class="col-title-text col-title-text2">
              <div class="col-title-text-subtitle">
                {{ currentTask.reviewRuleListName || childrenData.reviewRuleListName }}（{{ ruleTotal }}）
              </div>

              <div v-if="saveOngoing && !errorCode" class="col-title-text-progress">
                {{ STATUS_TEXT_MAP[currentTask.status] }} {{ ongoing }}/{{ ruleTotal }} <loading-icon />
                <div style="font-size: 12px; color: #999; margin-top: 4px">
                  运行时间: <time-display :created-time="currentTask.createdTime" />
                </div>
              </div>
              <div v-else-if="saveCompleted && !errorCode" class="col-title-text-progress-bar">
                <div class="col-title-text-progress-bar-text">
                  <t-tag theme="primary" variant="light">已完成</t-tag>
                </div>
                <div
                  v-permission="menuBlockList.includes('EXPORT')"
                  class="col-title-text-progress-bar-export"
                  @click="handleExport"
                >
                  导出结果
                </div>
              </div>
            </div>
          </div>
          <div v-if="!errorCode" class="col-l-body" style="padding-right: 10px">
            <template v-if="percentage > 0 && percentage < 100">
              <div class="tips-1">将于文档解析完成后开始审查</div>
            </template>
            <div v-if="saveOngoing" class="tips-2">*任务进行中，离开当前页面不会影响任务进行</div>
            <div v-if="percentage > 0 && percentage === 100" class="col-l-body-content">
              <div v-if="saveCompleted" class="tips">本次审查已完成，大模型处理结果仅供参考，请注意复核。</div>
              <t-tabs v-model="tabActive">
                <t-tab-panel value="ALL" :label="`全部 (${messages.length})`"> </t-tab-panel>
                <t-tab-panel value="FAILED" :label="`存在风险 (${riskCount})`"> </t-tab-panel>
                <t-tab-panel value="PASSED" :label="`通过 (${passCount})`"> </t-tab-panel>
              </t-tabs>
              <t-radio-group v-model="riskActive" class="radio-group">
                <t-radio-button value="ALL">全部</t-radio-button>
                <t-radio-button :value="RISK.HIGH" :label="`高风险 (${highRiskCount})`"></t-radio-button>
                <t-radio-button :value="RISK.MEDIUM" :label="`中风险 (${mediumRiskCount})`"></t-radio-button>
                <t-radio-button :value="RISK.LOW" :label="`低风险 (${lowRiskCount})`"></t-radio-button>
              </t-radio-group>
              <div class="result-box-overflow">
                <div v-for="item in filteredMessages" :key="item.bizId" class="result-box">
                  <div class="result-status">
                    <t-button
                      v-if="item.status === STATUS.FAILED"
                      style="min-width: 40px"
                      :loading="item.loading"
                      theme="danger"
                      variant="outline"
                      size="small"
                      @click="handleRetry(item)"
                    >
                      重试
                    </t-button>
                    <template v-else>
                      <t-button
                        v-if="item.result === 'PASSED'"
                        style="min-width: 62px"
                        theme="primary"
                        variant="outline"
                        size="small"
                      >
                        通过
                        <template #suffix>
                          <check-icon />
                        </template>
                      </t-button>
                      <t-button
                        v-else-if="item.result === 'FAILED'"
                        style="min-width: 86px"
                        theme="danger"
                        variant="outline"
                        size="small"
                      >
                        存在风险
                        <template #suffix>
                          <error-icon />
                        </template>
                      </t-button>
                    </template>
                    <span class="text">
                      <span>{{ item.reviewRuleName }}</span>
                      <t-tooltip theme="primary" :duration="3000" :content="getText(item.reviewRuleId)">
                        <info-circle-icon style="font-size: 14px; color: var(--td-brand-color)" />
                      </t-tooltip>
                    </span>
                    <span v-if="item.riskLevel === RISK.LOW" class="risk-level">#低风险</span>
                    <span v-if="item.riskLevel === RISK.MEDIUM" class="risk-level level2">#中风险</span>
                    <span v-if="item.riskLevel === RISK.HIGH" class="risk-level level3">#高风险</span>
                  </div>
                  <div class="result-description">说明: {{ item.comments }}</div>
                  <div class="original-text">
                    相关原文:
                    <t-tooltip
                      v-for="(val, index) of item.sourceText"
                      :key="index"
                      theme="primary"
                      placement="top-right"
                      :duration="3000"
                      overlay-class-name="tooltip-highlight-text"
                      destroy-on-close
                    >
                      <template #content>
                        <div class="text">
                          {{ val?.highlight_text }}
                        </div>
                      </template>
                      <a
                        :class="[{ primary: item.result === 'PASSED' }]"
                        href="JavaScript:void(0);"
                        style="margin: 0 5px"
                        @click="handleClickSourceText(val)"
                      >
                        原文{{ index + 1 }}</a
                      >
                    </t-tooltip>
                  </div>
                  <t-button
                    v-if="item.sourceText?.length"
                    v-permission="menuBlockList.includes('FEEDBACK')"
                    theme="primary"
                    variant="text"
                    size="small"
                    class="feedback-btn"
                    @click="openFeedback(item)"
                  >
                    错误反馈
                    <template #icon>
                      <edit2-icon />
                    </template>
                  </t-button>
                </div>
                <t-empty v-if="!filteredMessages.length" style="margin-top: 224px" />
              </div>
            </div>
          </div>
        </t-col>
      </t-row>
    </t-card>
    <error-feedback-dialog
      v-model:visible="showFeedback"
      v-model:related-docs="relatedDocs"
      v-model:related-id="relatedId"
      :review-item-title="relatedTitle"
      @submit="handleFeedbackSubmit"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  CatalogIcon,
  CheckIcon,
  ChevronLeftSIcon,
  Edit2Icon,
  ErrorIcon,
  InfoCircleIcon,
  LoadingIcon,
} from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onDeactivated, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { getById } from '@/api/modules/rule';
import { exportExcel, retryById, singleRuleReview, taskGetById, taskSavePost } from '@/api/modules/task';
import { MessageProps, ReviewRequest, TaskItem } from '@/api/types/task';
import TimeDisplay from '@/components/time-display';

import { REVIEW_STATUS, RISK, STATUS, STATUS_TEXT_MAP } from '../constants/index';
import { useFeedback } from '../constants/useFeedback';
import { usePreview } from '../constants/usePreview';
import ErrorFeedbackDialog from './FeedbackDialog.vue';
// 引入 Vue Router 的 useRouter 和 useRoute 方法，用于处理路由跳转和获取当前路由信息
const router = useRouter();
const route = useRoute();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];
const id = ref<string>(route.query.taskId as string);
// 存储审查消息的响应式数组
const messages = ref<MessageProps[]>([]);
// 审查规则列表
const ruleList = ref<any>([]);

// 定义组件 props，接收来自父组件的 childrenData（可能是 ReviewRequest 类型）
const props = defineProps<{
  childrenData?: ReviewRequest;
}>();
const emits = defineEmits(['update:childrenData']);

// 获取文档预览 进度条 公共方法的一个use
const {
  highlightedContent,
  tabIndex,
  customLoading,
  percentage,
  handlePreviewUrl,
  handleClickSourceText,
  isParsableJson,
  startLoadingProgress,
  stopLoadingProgress,
  closeLoading,
  tabClick,
  getFileContent,
} = usePreview();

// 错误反馈逻辑use
const { showFeedback, relatedId, relatedTitle, relatedDocs, openFeedback, handleFeedbackSubmit } = useFeedback();

// 错误码和错误信息状态，用于控制显示不同的错误提示
const errorCode = ref('');
const errMessage = ref('任务失败');

// 当前任务数据，初始为空对象
const currentTask = ref<TaskItem>({} as TaskItem);

/**
 * 计算规则总数（includedRules），根据 currentTask 或 props.childrenData 获取
 * @returns {number} 规则数量
 */
const ruleTotal = computed(() => {
  if (currentTask.value && currentTask.value.includedRules) {
    const includedRules = currentTask.value.includedRules.split(',');
    return includedRules.length;
  }
  if (props.childrenData && props.childrenData.includedRules) {
    const includedRules = props.childrenData.includedRules.split(',');
    return includedRules.length;
  }
  return 0;
});

// 当前选中的标签页，用于控制展示的消息类型（全部、通过、有风险）
const tabActive = ref('ALL');

// 当前选中的风险等级，用于进一步筛选消息（全部、高风险、中风险、低风险）
const riskActive = ref('ALL');

/** 审查任务进行中状态 */
const saveOngoing = computed(() => {
  const statusList: string[] = [STATUS.ONGOING, STATUS.REVIEWING, STATUS.ANALYSING, STATUS.ANALYZED];
  return statusList.includes(currentTask.value.status);
});
/** 审查任务已完成状态 */
const saveCompleted = computed(() => {
  return currentTask.value.status === STATUS.COMPLETED;
});
/**
 * 计算当前任务中已完成的审查项数量。
 * @returns {number} 已完成的审查项数量。
 */
const ongoing = computed(() => {
  if (messages.value.length) {
    return messages.value.filter((item) => item.status !== REVIEW_STATUS.REVIEWING).length;
  }
  return 0;
});
/**
 * 计算当前任务中存在风险的审查项数量。
 * @returns {number} 存在风险的审查项数量。
 */
const riskCount = computed(() => {
  if (messages.value.length) {
    return messages.value.filter((item) => item.result === 'FAILED').length;
  }
  return 0;
});

/**
 * 计算当前任务中已通过的审查项数量。
 * @returns {number} 已通过的审查项数量。
 */
const passCount = computed(() => {
  if (messages.value.length) {
    return messages.value.filter((item) => item.result === 'PASSED').length;
  }
  return 0;
});
/**
 * 根据 tabActive 的值对消息进行初步过滤。
 *
 * 过滤条件：
 * - 'ALL'：返回所有消息
 * - REVIEW_STATUS.COMPLETED：仅返回已通过的消息
 * - RISK_LEVEL.RISK：仅返回存在风险的消息
 */
const tabFilterList = computed(() => {
  const list = messages.value;

  return list.filter((item) => {
    if (tabActive.value === 'ALL') {
      return true; // 返回全部消息
    }
    if (tabActive.value === 'PASSED') {
      return item.result === 'PASSED'; // 已完成（通过）的消息
    }
    if (tabActive.value === 'FAILED') {
      return item.result === 'FAILED'; // 存在风险的消息
    }
    return false;
  });
});

/**
 * 在 tabFilterList 基础上，进一步根据 riskActive 筛选消息。
 *
 * 最终筛选结果由两个维度控制：
 * - tabActive: 控制任务状态或风险标识（如全部、通过、有风险）
 * - riskActive: 控制具体风险等级（高、中、低）
 */
const filteredMessages = computed(() => {
  const list = tabFilterList.value;

  return list.filter((item) => {
    if (riskActive.value === 'ALL') {
      return true; // 所有风险等级
    }
    if (riskActive.value === RISK.HIGH) {
      return item.riskLevel === RISK.HIGH; // 高风险
    }
    if (riskActive.value === RISK.MEDIUM) {
      return item.riskLevel === RISK.MEDIUM; // 中风险
    }
    if (riskActive.value === RISK.LOW) {
      return item.riskLevel === RISK.LOW; // 低风险
    }
    return false;
  });
});

/**
 * 统计当前 tab 下各风险等级的数量。
 * highRiskCount: 当前 tab 下的高风险项数量
 */
const highRiskCount = computed(() => {
  return tabFilterList.value.filter((item) => item.riskLevel === RISK.HIGH).length;
});

/**
 * mediumRiskCount: 当前 tab 下的中风险项数量
 */
const mediumRiskCount = computed(() => {
  return tabFilterList.value.filter((item) => item.riskLevel === RISK.MEDIUM).length;
});

/**
 * lowRiskCount: 当前 tab 下的低风险项数量
 */
const lowRiskCount = computed(() => {
  return tabFilterList.value.filter((item) => item.riskLevel === RISK.LOW).length;
});
// 是否显示文件预览
const isFilePreview = computed(() => {
  return !errorCode.value && percentage.value >= 100;
});
/** 文件导出 */
const handleExport = () => {
  if (!currentTask.value.id) {
    MessagePlugin.error(`导出审查结果失败！`);
    return;
  }
  const fileName = `${currentTask.value?.fileName?.split('.')?.slice(0, -1)?.join('.')} 审查结果`;
  exportExcel(currentTask.value.id, fileName).then(() => {
    MessagePlugin.success(`导出 ${fileName} 成功`);
  });
};
// 重试审查项
const handleRetry = (row: MessageProps) => {
  row.loading = true;
  singleRuleReview(row.id)
    .then(async (res) => {
      const { comments, result, riskLevel, sourceText, status } = res;
      row.comments = comments;
      row.result = result;
      row.riskLevel = riskLevel;
      row.status = status;
      if (await isParsableJson(sourceText)) {
        row.sourceText = JSON.parse(sourceText);
      }
    })
    .finally(() => {
      row.loading = false;
    });
};

/** 查询任务详情 */
const timeOut = ref(null);
const onSearch = (id: string, type: boolean = true) => {
  if (!id) return;

  taskGetById(id)
    .then((res: any) => {
      if (!res) return;

      res.resultsVoList = res.resultsVoList || [];
      res.resultsVoList.forEach(async (item: any) => {
        if (await isParsableJson(item.sourceText)) {
          item.sourceText = JSON.parse(item.sourceText);
        }
      });

      if (res.reviewRuleListId && type) {
        getRuleList(res.reviewRuleListId);
      }

      // 获取任务详情
      currentTask.value = res;
      messages.value = res.resultsVoList || [];
      errorCode.value = res.errorCode && res.status === STATUS.FAILED ? res.errorCode : '';
      errMessage.value = res.errorMessage;

      // 不是分析中就停止进度条
      if (res.status !== STATUS.ANALYSING) {
        stopLoadingProgress();
        percentage.value = 100;
      }

      // 进行中 排队中 审查中 分析中 分析完成
      const statusList: string[] = [STATUS.ONGOING, STATUS.REVIEWING, STATUS.QUEUED, STATUS.ANALYSING, STATUS.ANALYZED];
      clearTimeout(timeOut.value);
      if (statusList.includes(res.status) && !errorCode.value) {
        timeOut.value = setTimeout(() => {
          onSearch(id, false);
        }, 2000);
      }
    })
    .catch(() => {});
};

// 获取规则列表
const getRuleList = (id: string) => {
  ruleList.value = [];
  getById(id).then((res: any) => {
    if (res.ruleVos?.length) {
      ruleList.value = res.ruleVos;
    }
  });
};
// 获取规则说明
const getText = (id: string | number) => {
  const item = ruleList.value.find((item: any) => item.id === id);
  if (item) {
    return item.rule;
  }
  return '';
};

onMounted(() => {
  if (id.value) {
    startLoadingProgress(); // 开始模拟进度条
    onSearch(id.value);
  }
});
const handleBack = () => {
  router.push('/contract-review/task-management');
};
const reloadPageData = () => {
  // 重试任务
  errorCode.value = '';
  errMessage.value = '';
  currentTask.value.resultsVoList = [];
  messages.value = [];
  startLoadingProgress(); // 开始模拟进度条
  // 重试任务
  retryById(id.value).then((res: any) => {
    if (res) onSearch(id.value);
  });
};

const handleSave = (params: ReviewRequest) => {
  taskSavePost(params)
    .then((res: any) => {
      if (res) {
        id.value = res;
        onSearch(id.value);
      }
    })
    .catch((error) => {
      errorCode.value = `${error.data?.errCode ? error.data?.errCode : error.status}`;
      errMessage.value = error.data?.errMessage || '任务不存在';
      percentage.value = 0;
    });
};

watch(
  () => props.childrenData,
  (val: ReviewRequest) => {
    if (val.fileId && !id.value) {
      // 创建任务
      startLoadingProgress(); // 开始模拟进度条
      handleSave(val);
    }
  },
  { immediate: true },
);
// 预览文件
watch(
  () => isFilePreview.value,
  (val: boolean) => {
    if (val) {
      handlePreviewUrl(props.childrenData.fileId || currentTask.value.fileId);
      getFileContent(props.childrenData.fileId || currentTask.value.fileId);
    }
  },
);

// 离开当前组件销毁timeOut
onDeactivated(() => {
  id.value = '';
  clearTimeout(timeOut.value);
  emits('update:childrenData', {});
  closeLoading();
});

watch(
  () => route.query.taskId,
  () => {
    const taskId = route.query.taskId as string;
    id.value = taskId;
    if (taskId) {
      onSearch(taskId);
    }
  },
  { immediate: true },
);
</script>
<style lang="less" scoped>
@import '../less/review-task.less';
</style>
<style lang="less">
.tooltip-highlight-text {
  .t-popup__content {
    .text {
      white-space: pre-line;
      max-height: 50vh;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}
.review .highlight {
  background-color: yellow;
  transition: background-color 0.2s;
}
.review .current-highlight {
  background-color: yellow;
}

.web-office-iframe {
  width: 100% !important;
  height: 100% !important;
}
</style>
