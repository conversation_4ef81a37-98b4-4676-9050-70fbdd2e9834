<template>
  <t-dialog
    :visible="visible"
    header="错误反馈"
    width="700px"
    :close-on-overlay-click="false"
    class="feedback-dialog"
    @update:visible="$emit('update:visible', $event)"
    @close="onCancel"
  >
    <t-form ref="form" class="feedback-form" :data="formData" :rules="rules">
      <t-form-item label="审查项：">{{ reviewItemTitle }}</t-form-item>
      <t-form-item label="相关原文：" name="contractContent">
        <div style="width: 100%">
          <div class="form-row">
            <t-button-group>
              <t-button
                v-for="(doc, idx) in relatedDocs"
                :key="idx"
                :theme="selectedDocIdx === idx ? 'primary' : 'default'"
                size="small"
                @click="selectDoc(idx)"
              >
                {{ doc.label }}
              </t-button>
            </t-button-group>
            <t-button size="small" style="margin-left: auto; min-width: 78px" @click="addDoc">手动补充 +</t-button>
          </div>

          <t-textarea
            v-if="readonly"
            v-model="selectedDocContent"
            readonly
            :autosize="{ minRows: 3, maxRows: 5 }"
            style="width: 100%"
          />
          <t-textarea
            v-else
            v-model="selectedDocContent"
            placeholder="请输入内容"
            :maxlength="1000"
            :autosize="{ minRows: 3, maxRows: 5 }"
            style="width: 100%"
          />
        </div>
      </t-form-item>
      <t-form-item label="正确结果：" name="correctResult">
        <t-radio-group v-model="formData.correctResult">
          <t-radio :value="RISK_LEVEL.RISK">存在风险</t-radio>
          <t-radio :value="RISK_LEVEL.NONE">无风险</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="理由说明：" name="reviewDescription">
        <t-textarea
          v-model="formData.reviewDescription"
          :maxlength="200"
          :autosize="{ minRows: 3, maxRows: 5 }"
          placeholder="请输入判断说明，AI将学习该审查逻辑，并在后续任务中显著提升对此类情况的审查准确性"
        />
      </t-form-item>
    </t-form>
    <template #footer>
      <div style="text-align: center">
        <t-button theme="primary" @click="onSubmit">提交</t-button>
        <t-button theme="default" variant="base" @click="onCancel">取消</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
import { FormRule } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';

import { ExampleFeedback } from '@/api/types/rule';

import { RISK_LEVEL } from '../constants/index';

const props = defineProps<{
  visible: boolean;
  relatedId: string;
  reviewItemTitle: string;
  relatedDocs: { label: string; content: string; readonly: boolean }[];
}>();
const emits = defineEmits([
  'update:visible',
  'submit',
  'cancel',
  'update:relatedDocs',
  'update:relatedId',
  'update:reviewItemTitle',
]);

// 定义当前选中文档的索引，默认为第一个文档（索引0）
const selectedDocIdx = ref(0);

// 表单引用，用于后续表单验证和操作
const form = ref();

// 表单数据对象，包含审查结果、理由说明、合同内容等字段
const formData = ref<ExampleFeedback>({
  reviewRuleId: '', // 审查规则ID
  correctResult: 'NONE', // 正确结果，默认值表示无风险
  reviewDescription: '', // 理由说明
  contractContent: '', // 合同内容
});

// 表单校验规则定义
const rules: Record<string, FormRule[]> = {
  correctResult: [{ required: true, message: '不能为空', type: 'error' }],
  reviewDescription: [{ required: true, message: '不能为空', type: 'error' }],
  contractContent: [{ required: true, message: '不能为空', type: 'error' }],
};

// 计算属性：获取或设置当前选中文档的内容
const selectedDocContent = computed({
  get: () => {
    return props.relatedDocs[selectedDocIdx.value]?.content || ''; // 获取内容，若不存在则返回空字符串
  },
  set: (newValue: string) => {
    if (props.relatedDocs[selectedDocIdx.value]) {
      const updatedDocs = [...props.relatedDocs]; // 创建副本以避免直接修改props
      updatedDocs[selectedDocIdx.value] = {
        ...updatedDocs[selectedDocIdx.value],
        content: newValue, // 更新选中文档的内容
      };
      emits('update:relatedDocs', updatedDocs); // 触发事件通知父组件更新
    }
  },
});

// 监听selectedDocContent的变化，并同步到表单数据中
watch(
  () => selectedDocContent.value,
  (newValue) => {
    formData.value.contractContent = newValue; // 将最新文档内容赋值给表单数据
  },
);

// 计算属性：检查当前文档是否只读
const readonly = computed(() => {
  return props.relatedDocs[selectedDocIdx.value]?.readonly ?? true; // 默认true，如果未定义也视为只读
});

// 切换选中文档的方法
function selectDoc(idx: number) {
  selectedDocIdx.value = idx; // 设置当前选中的文档索引
}

// 提交按钮点击处理函数
async function onSubmit() {
  const validateResult = await form.value.validate(); // 表单验证
  if (validateResult === true) {
    formData.value.reviewRuleId = props.relatedId; // 设置reviewRuleId
    emits('submit', formData.value); // 触发提交事件并传递表单数据
  }
}

// 取消按钮点击处理函数
function onCancel() {
  formData.value = {
    reviewRuleId: '',
    correctResult: 'NONE',
    reviewDescription: '',
    contractContent: '',
  };
  form.value.clearValidate();
  emits('update:relatedId', ''); // 清空相关ID
  emits('update:reviewItemTitle', ''); // 清空审查项标题
  emits('update:relatedDocs', []); // 清空相关文档列表
  emits('cancel'); // 触发取消事件
  emits('update:visible', false); // 关闭对话框
}

// 手动添加新文档的方法
function addDoc() {
  const item = {
    label: `补充原文${props.relatedDocs.length + 1}`, // 动态生成标签名称
    content: '', // 初始化为空内容
    readonly: false, // 新增文档默认可编辑
  };
  const updatedDocs = [...props.relatedDocs]; // 创建副本以避免直接修改props
  updatedDocs.push(item); // 添加新的文档条目
  selectDoc(updatedDocs.length - 1); // 自动切换到新添加的文档
  emits('update:relatedDocs', updatedDocs); // 触发事件通知父组件更新
}

defineExpose({
  selectDoc,
  onSubmit,
  onCancel,
  addDoc,
});
</script>

<style lang="less" scoped>
.feedback-form {
  font-size: 14px;
}
.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  :deep(t-button-group .t-button) {
    margin-left: 0 !important;
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.feedback-dialog::v-deep {
  .t-dialog__body {
    overflow: hidden;
  }
}
</style>
