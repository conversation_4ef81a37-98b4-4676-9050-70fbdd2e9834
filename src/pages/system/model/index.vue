<template>
  <div class="api-configuration">
    <t-card class="list-card-container">
      <div class="route-title">
        {{ route.meta.title }}
      </div>
      <t-tabs v-model="tabsValue">
        <!-- 默认插槽 和 具名插槽（panel）都是用来渲染面板内容 -->
        <t-tab-panel value="first" label="模型配置" :destroy-on-hide="false">
          <div class="table-header">
            <t-button v-permission="menuBlockList.includes('ADD_CONFIG')" theme="primary" @click="onCreate1">
              新增配置
            </t-button>
          </div>

          <t-table
            :data="tableData"
            :columns="columns"
            :loading="loading"
            :pagination="pagination"
            row-key="id"
            @page-change="onPageChange"
          >
            <template #id="{ row }">
              {{ row.id?.padStart(5, '0') }}
            </template>
            <template #highSpeedModelName="{ row }">
              <div>
                <div>高性能：{{ row.highSpeedModelName }}</div>
                <div>高精度：{{ row.highQualityModelName }}</div>
                <div>审查规则优化：{{ row.ioModelName }}</div>
              </div>
            </template>
            <template #tenantList="{ row }">
              {{ row.tenantList?.length }}
            </template>
            <template #op="{ row }">
              <t-space>
                <t-link theme="primary" hover="color" @click="onDetail(row)">查看</t-link>
                <t-popconfirm theme="danger" :content="`确定要删除 ${row.name} 吗？`" @confirm="onDelete(row)">
                  <t-link
                    v-if="row.code !== 'DEFAULT'"
                    v-permission="menuBlockList.includes('DELETE_CONFIG')"
                    theme="danger"
                    hover="color"
                  >
                    删除
                  </t-link>
                </t-popconfirm>
              </t-space>
            </template>
          </t-table>
        </t-tab-panel>
        <t-tab-panel value="second" label="模型资源" :destroy-on-hide="false">
          <div class="table-header">
            <t-button v-permission="menuBlockList.includes('ADD_MODEL')" theme="primary" @click="onCreate2">
              添加模型
            </t-button>
          </div>

          <t-table
            :data="tableData2"
            :columns="columns2"
            :loading="loading"
            :pagination="pagination2"
            row-key="id"
            @page-change="onPageChange2"
          >
            <template #enable="{ row }">
              <t-tag :theme="row.enable ? 'success' : 'danger'">
                {{ row.enable ? '启用' : '禁用' }}
              </t-tag>
            </template>
            <template #op="{ row }">
              <t-space>
                <t-link
                  v-permission="menuBlockList.includes('EDIT_MODEL')"
                  theme="primary"
                  hover="color"
                  @click="onEdit(row)"
                  >编辑</t-link
                >
                <t-popconfirm theme="danger" :content="`确定要删除 ${row.modelName} 吗？`" @confirm="onDelete2(row)">
                  <t-link v-permission="menuBlockList.includes('DELETE_MODEL')" theme="danger" hover="color">
                    删除
                  </t-link>
                </t-popconfirm>
              </t-space>
            </template>
          </t-table>
        </t-tab-panel>
      </t-tabs>
    </t-card>

    <edit-model v-model:visible="visible" v-model:id="rowId" @confirm="handleEditModelConfirm"></edit-model>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Model',
};
</script>
<script lang="ts" setup>
import type { PageInfo, PrimaryTableCol } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { onActivated, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { llmConfigDeleteById, llmConfigPageByVo, llmSourceDeleteById, llmSourcePageByVo } from '@/api/modules/model';

import EditModel from './components/editModel.vue';
// 获取当前路由信息
const route = useRoute();
const router = useRouter();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

const visible = ref(false);
const rowId = ref('');
interface PaginationState {
  total: number;
  size: number;
  current: number;
}
const tabsValue = ref('first');
const loading = ref(false);
const tableData = ref([]);
const tableData2 = ref([]);
const pagination = reactive<PaginationState>({
  total: 0,
  size: 10,
  current: 1,
});
const pagination2 = reactive<PaginationState>({
  total: 0,
  size: 10,
  current: 1,
});

const columns: PrimaryTableCol[] = [
  { colKey: 'name', title: '规则名称', width: 300, ellipsis: true, fixed: 'left' },
  { colKey: 'id', title: '规则ID', width: 200 },
  { colKey: 'highSpeedModelName', title: '模型', width: 300 },
  { colKey: 'tenantList', title: '租户数量', width: 200 },
  { colKey: 'createdTime', title: '创建时间', width: 200 },
  { colKey: 'op', title: '操作', width: 120, fixed: 'right' },
];
const columns2: PrimaryTableCol[] = [
  { colKey: 'modelName', title: '模型名称', width: 400, ellipsis: true, fixed: 'left' },
  { colKey: 'apiKey', title: 'API Key', width: 400 },
  { colKey: 'temperature', title: 'Temperature', width: 200 },
  { colKey: 'createdTime', title: '创建时间', width: 200 },
  { colKey: 'enable', title: '状态', width: 200 },
  { colKey: 'op', title: '操作', width: 120, fixed: 'right' },
];

const onSearch = async () => {
  loading.value = true;
  try {
    await fetchTableData();
  } finally {
    loading.value = false;
  }
};
const onSearch2 = async () => {
  loading.value = true;
  try {
    await fetchTableData2();
  } finally {
    loading.value = false;
  }
};

const onPageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  if (pageInfo.pageSize) pagination.size = pageInfo.pageSize;
  onSearch();
};
const onPageChange2 = (pageInfo: PageInfo) => {
  pagination2.current = pageInfo.current;
  if (pageInfo.pageSize) pagination2.size = pageInfo.pageSize;
  onSearch2();
};

const onCreate1 = () => {
  router.push('/system/model-create?type=add');
};
const onCreate2 = () => {
  rowId.value = '';
  visible.value = true;
};

const onDelete = (row: any) => {
  llmConfigDeleteById(row.id).then((res) => {
    if (!res) {
      MessagePlugin.error('删除失败');
      return;
    }
    MessagePlugin.success('删除成功');
    onSearch();
  });
};
const onDelete2 = (row: any) => {
  llmSourceDeleteById(row.id).then((res) => {
    if (!res) {
      MessagePlugin.error('删除失败');
      return;
    }
    MessagePlugin.success('删除成功');
    onSearch2();
  });
};
const fetchTableData = async () => {
  try {
    const { current, size } = pagination;
    const res = await llmConfigPageByVo({ current, size });
    tableData.value = res.records || [];
    pagination.total = Number(res.total) || 0;
  } catch (error) {
    console.error('获取列表失败:', error);
  }
};
const fetchTableData2 = async () => {
  try {
    const { current, size } = pagination;
    const res = await llmSourcePageByVo({ current, size });
    tableData2.value = res.records || [];
    pagination2.total = Number(res.total) || 0;
  } catch (error) {
    console.error('获取列表失败:', error);
  }
};
const onEdit = (row: any) => {
  rowId.value = row.id;
  visible.value = true;
};
const handleEditModelConfirm = () => {
  rowId.value = '';
  pagination2.current = 1;
  onSearch2();
};

const onDetail = (row: any) => {
  router.push(`/system/model-create?type=edit&id=${row.id}`);
};

onActivated(() => {
  onSearch();
  onSearch2();
});
</script>

<style lang="less" scoped>
.api-configuration {
  padding: 0;
  .list-card-container {
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);

    :deep(.t-card__body) {
      padding: 0;
    }
  }
  .route-title {
    height: 22px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
}
.table-header {
  margin: 10px 0;
  text-align: right;
}
.api-key-content {
  .api-key-tip {
    color: var(--td-gray-color-6);
    margin-bottom: 16px;
  }
  .api-key-body {
    padding: 0 10px;
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 4px;
    background-color: #e7e8f0;
    .api-key-value {
      flex: 1;
      width: 0;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .api-key-h {
      min-width: 20px;
      text-align: center;
    }
    .api-key-copy {
      cursor: pointer;
      font-size: 16px;
    }
  }
}
</style>
