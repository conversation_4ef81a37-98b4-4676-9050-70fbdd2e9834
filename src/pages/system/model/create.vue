<template>
  <div v-loading="loading" class="model-create">
    <div class="route-title">模型配置 > 配置管理</div>

    <t-form
      ref="form"
      :key="isEdit ? 'edit' : 'add'"
      :rules="isEdit ? rules : {}"
      :data="formData"
      label-align="top"
      class="form-dome"
    >
      <t-form-item label="服务类型"> 合同审查 </t-form-item>
      <t-form-item label="规则名称" name="name">
        <t-input
          v-if="isEdit"
          v-model="formData.name"
          maxlength="30"
          clearable
          show-limit-number
          placeholder="请输入规则名称"
        />
        <template v-else> {{ formData.name }}</template>
      </t-form-item>
      <t-form-item label="备注说明" name="remark">
        <t-textarea
          v-if="isEdit"
          v-model="formData.remark"
          clearable
          maxlength="1000"
          :autosize="{ minRows: 3, maxRows: 5 }"
          show-limit-number
          placeholder="请输入备注说明"
        />
        <template v-else> {{ formData.remark }}</template>
      </t-form-item>
      <t-divider align="left">模型配置</t-divider>
      <t-form-item label="高性能模式" name="highSpeedModelId">
        <t-select
          v-if="isEdit"
          v-model="formData.highSpeedModelId"
          clearable
          filterable
          placeholder="请选择高性能模式"
          style="width: 300px"
        >
          <t-option v-for="item of options" :key="item.id" :label="item.modelName" :value="item.id" />
        </t-select>
        <template v-else> {{ formData.highSpeedModelName }}</template>
      </t-form-item>
      <t-form-item label="高精度模式" name="highQualityModelId">
        <t-select
          v-if="isEdit"
          v-model="formData.highQualityModelId"
          clearable
          filterable
          placeholder="请选择高精度模式"
          style="width: 300px"
        >
          <t-option
            v-for="item of options"
            :key="item.id"
            :label="item.modelName"
            :value="item.id"
            :disabled="item.id !== '3'"
          />
        </t-select>
        <template v-else> {{ formData.highQualityModelName }}</template>
      </t-form-item>
      <t-form-item label="审查规则优化" name="ioModelId">
        <t-select
          v-if="isEdit"
          v-model="formData.ioModelId"
          clearable
          filterable
          placeholder="请选择审查规则优化"
          style="width: 300px"
        >
          <t-option v-for="item of options" :key="item.id" :label="item.modelName" :value="item.id" />
        </t-select>
        <template v-else> {{ formData.ioModelName }}</template>
      </t-form-item>

      <!-- <t-form-item label="适用租户" name="name">
        <div class="model-config">
          <div v-if="isEdit" class="model-config-item">
            <t-button theme="primary" size="small" style="width: 100px" @click="handleTenantOpen">添加租户</t-button>
          </div>
          <div class="model-config-item" style="margin-top: 12px">1</div>
        </div>
      </t-form-item> -->
      <t-form-item>
        <t-button
          v-if="paramsId && !isEdit"
          v-permission="menuBlockList.includes('EDIT')"
          theme="primary"
          style="width: 100px; margin-left: auto"
          @click="handleEdit"
        >
          编辑
        </t-button>
        <t-space v-if="isEdit" style="padding-left: 200px">
          <t-button style="width: 100px" theme="primary" @click="handleSave">保存</t-button>
          <t-button theme="default" style="width: 100px" @click="handleClose">取消</t-button>
        </t-space>
      </t-form-item>
    </t-form>

    <!-- 租户选择框 -->
    <tenant-dialog
      v-model:visible="visible"
      v-model:values="formData.tenantList"
      @confirm="handleConfirm"
    ></tenant-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ModelCreate',
};
</script>
<script lang="ts" setup>
import { type FormRule, MessagePlugin } from 'tdesign-vue-next';
import { onActivated, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { llmConfigGetById, llmConfigSave, llmConfigUpdateById, llmSourcePageByVo } from '@/api/modules/model';
import { llmConfigObject } from '@/api/types/model';
import TenantDialog from '@/components/tenant-dialog/index.vue';

const loading = ref(false);
const route = useRoute();
const router = useRouter();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

const paramsId = ref(''); //   参数id
const isEdit = ref(false); //  是否是编辑
const options = ref([]); //  下拉框数据
const form = ref(); //  表单实例
const formData = ref<llmConfigObject>({
  highQualityModelId: '3',
  highQualityModelName: '',
  highSpeedModelId: '',
  highSpeedModelName: '',
  ioModelId: '',
  ioModelName: '',
  name: '',
  remark: '',
}); //  表单数据
const formDataCopy = ref<llmConfigObject>({
  highQualityModelId: '3',
  highQualityModelName: '',
  highSpeedModelId: '',
  highSpeedModelName: '',
  ioModelId: '',
  ioModelName: '',
  name: '',
  remark: '',
}); //  表单数据

const rules: Record<string, FormRule[]> = {
  name: [{ required: true, message: '请输入规则名称', type: 'error' }],
  remark: [{ required: true, message: '请输入备注说明', type: 'error' }],
  highSpeedModelId: [{ required: true, message: '请选择高精度模型', type: 'error' }],
  highQualityModelId: [{ required: true, message: '请选择高性能模型', type: 'error' }],
  ioModelId: [{ required: true, message: '请选择审查规则优化', type: 'error' }],
}; //  表单验证规则

// 编辑按钮点击处理函数
const handleEdit = () => {
  isEdit.value = true;
};
// 关闭按钮点击处理函数
const handleClose = () => {
  isEdit.value = false;
  if (!paramsId.value) {
    router.push({ path: '/system/model' });
  } else {
    formData.value = JSON.parse(JSON.stringify(formDataCopy.value));
  }
};
const onGetById = (id: string) => {
  if (!id) return;

  llmConfigGetById(id)
    .then((res) => {
      formData.value = {
        highQualityModelId: res.highQualityModelId || '3',
        highQualityModelName: res.highQualityModelName || '',
        highSpeedModelId: res.highSpeedModelId || '',
        highSpeedModelName: res.highSpeedModelName || '',
        ioModelId: res.ioModelId || '',
        ioModelName: res.ioModelName || '',
        name: res.name || '',
        remark: res.remark || '',
        id: res.id || '',
      };
      formDataCopy.value = JSON.parse(JSON.stringify(formData.value));
    })
    .finally(() => {
      loading.value = false;
    });
};
const visible = ref(false);
// 打开租户选择对话框，初始化已选租户数据
// const handleTenantOpen = () => {
//   visible.value = true;
// };
// 确认租户选择
const handleConfirm = (data: any) => {
  formData.value.tenantList = data;
};
// 保存按钮点击处理函数
const handleSave = async () => {
  const validateResult = await form.value.validate();
  if (validateResult === true) {
    formData.value.highQualityModelName = options.value.find(
      (item) => item.id === formData.value.highQualityModelId,
    )?.modelName;
    formData.value.highSpeedModelName = options.value.find(
      (item) => item.id === formData.value.highSpeedModelId,
    )?.modelName;
    formData.value.ioModelName = options.value.find((item) => item.id === formData.value.ioModelId)?.modelName;

    if (paramsId.value) {
      llmConfigUpdateById(formData.value).then((res) => {
        if (res) {
          MessagePlugin.success('保存成功');
          router.push({ path: '/system/model' });
        } else {
          MessagePlugin.error('保存失败');
        }
      });
    } else {
      llmConfigSave(formData.value).then((res) => {
        if (res) {
          MessagePlugin.success('保存成功');
          router.push({ path: '/system/model' });
        } else {
          MessagePlugin.error('保存失败');
        }
      });
    }
  }
};

const getOptions = () => {
  llmSourcePageByVo({ current: 1, size: 100 }).then((res) => {
    options.value = res?.records || [];
  });
};

// 页面激活时处理函数
onActivated(() => {
  formData.value = {
    highQualityModelId: '3',
    highQualityModelName: '',
    highSpeedModelId: '',
    highSpeedModelName: '',
    ioModelId: '',
    ioModelName: '',
    name: '',
    remark: '',
  };
  getOptions();
  const { type, id } = route.query as { type: string; id: string };
  if (type === 'add') {
    isEdit.value = true;
    paramsId.value = '';
  } else if (type === 'edit') {
    loading.value = true;
    isEdit.value = false;
    paramsId.value = id;
    onGetById(id);
  }
});
</script>
<style lang="less" scoped>
.model-create {
  padding: 24px;
  min-height: 100%;
  background-color: var(--td-bg-color-container);
  border-radius: 6px;
  .route-title {
    height: 22px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
  .form-dome {
    width: 50%;
    min-width: 400px;
    max-width: 700px;
    margin-top: 10px;
    padding: 0 10px;
    .model-config-item {
      padding-left: 10px;
      .model-config-item-title {
        line-height: 30px;
      }
    }
  }
}
</style>
