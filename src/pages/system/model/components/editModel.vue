<template>
  <t-dialog
    header="大模型设置"
    top="5vh"
    :close-on-overlay-click="false"
    :visible="visible"
    width="800px"
    draggable
    :footer="false"
    @close="onClose"
  >
    <t-form ref="form" :rules="rules" :data="formData" label-align="left" label-width="80px" class="form-dome">
      <t-form-item label="模型名称" name="modelName">
        <t-input v-model="formData.modelName" maxlength="30" clearable show-limit-number placeholder="请输入模型名称" />
      </t-form-item>
      <t-form-item label="接口协议" name="apiMethod">
        <t-input v-model="formData.apiMethod" maxlength="30" clearable show-limit-number placeholder="请输入接口协议" />
      </t-form-item>
      <t-form-item label="BaseUrl" name="apiBaseUrl">
        <t-textarea
          v-model="formData.apiBaseUrl"
          clearable
          maxlength="300"
          :autosize="{ minRows: 2, maxRows: 5 }"
          placeholder="请输入BaseUrl"
        />
      </t-form-item>
      <t-form-item label="API KEY" name="apiKey">
        <t-textarea
          v-model="formData.apiKey"
          clearable
          maxlength="300"
          :autosize="{ minRows: 2, maxRows: 5 }"
          placeholder="请输入API KEY"
        />
      </t-form-item>
      <t-form-item label="超时时间" label-align="left" name="timeout">
        <t-input-number v-model="formData.timeout" :step="1" :min="1" suffix="秒" auto-width />
      </t-form-item>
      <t-form-item label="随机种子" label-align="left" name="seed">
        <t-input-number v-model="formData.seed" :step="1" :min="1" auto-width />
      </t-form-item>
      <t-form-item label="Temperature" label-align="left">
        <t-slider v-model="formData.temperature" class="slider-demo" :marks="marks" :show-tooltip="true" :step="1" />
      </t-form-item>

      <t-form-item label="启用管理" label-align="left">
        <t-radio-group v-model="formData.enable">
          <t-radio :value="1">启用</t-radio>
          <t-radio :value="0">禁用</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="额外配置">
        <t-textarea
          v-model="formData.extraBody"
          clearable
          maxlength="1000"
          :autosize="{ minRows: 2, maxRows: 5 }"
          placeholder="请输入额外配置"
        />
      </t-form-item>
      <t-form-item label="模型描述">
        <t-textarea
          v-model="formData.remark"
          clearable
          maxlength="1000"
          :autosize="{ minRows: 2, maxRows: 5 }"
          placeholder="请输入模型描述"
        />
      </t-form-item>
      <t-form-item style="text-align: right">
        <!-- <t-button theme="primary" style="margin-left: auto" @click="handleTest">连接测试</t-button> -->
        <t-button theme="primary" style="margin-left: auto" @click="handleSave">保存</t-button>
        <t-button theme="default" @click="onClose">取消</t-button>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
<script lang="ts" setup>
import { type FormRule, MessagePlugin } from 'tdesign-vue-next';
import { ref, watch } from 'vue';

import { llmSourceGetById, llmSourceSave, llmSourceUpdateById } from '@/api/modules/model';
import { ModelObject } from '@/api/types/model';
// 获取参数
const props = defineProps<{
  id?: string;
  visible: boolean;
}>();
// 事件
const emits = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm'): void;
}>();

const formData = ref<ModelObject>({
  apiBaseUrl: '',
  apiKey: '',
  apiMethod: '',
  enable: 1,
  modelName: '',
  timeout: 1,
  temperature: 1,
  remark: '',
  seed: 0,
  extraBody: 1,
}); //  表单数据
const apiKey = ref('');
const rules: Record<string, FormRule[]> = {
  modelName: [{ required: true, message: '请输入', type: 'error' }],
  apiKey: [{ required: true, message: '请输入', type: 'error' }],
  apiBaseUrl: [{ required: true, message: '请输入', type: 'error' }],
  apiMethod: [{ required: true, message: '请输入', type: 'error' }],
  timeout: [{ required: true, message: '请输入', type: 'error' }],
}; //  表单验证规则
const marks = ref({
  0: '精确',
  100: '随机',
});
const form = ref(); //  表单实例
// 关闭对话框
const onClose = () => {
  formData.value = {
    apiBaseUrl: '',
    apiKey: '',
    apiMethod: '',
    enable: 1,
    modelName: '',
    timeout: 0,
    temperature: 0,
    remark: '',
    seed: 0,
    extraBody: 1,
  };
  apiKey.value = '';
  form.value?.clearValidate();
  emits('update:visible', false); // 关闭对话框
};

const onGetById = (id: string) => {
  if (!id) return;

  llmSourceGetById(id).then((res) => {
    formData.value = {
      apiBaseUrl: res.apiBaseUrl || '',
      apiKey: res.apiKey || '',
      apiMethod: res.apiMethod || '',
      enable: res.enable ?? 1,
      modelName: res.modelName || '',
      timeout: res.timeout || 1,
      temperature: res.temperature || 1,
      remark: res.remark || '',
      id: res.id || '',
      seed: res.seed || 0,
      extraBody: res.extraBody || 1,
    };
    apiKey.value = res.apiKey;
  });
};
// 保存按钮点击处理函数
const paramsId = ref(''); //   参数id
const handleSave = async () => {
  const validateResult = await form.value.validate();
  if (validateResult === true) {
    if (paramsId.value) {
      const params = JSON.parse(JSON.stringify(formData.value));
      // 判断apiKey是否相同，相同则删除
      if (apiKey.value === params.apiKey) {
        delete params.apiKey;
      }
      llmSourceUpdateById(params).then((res) => {
        if (res) {
          MessagePlugin.success('保存成功');
          onClose();
          emits('confirm');
        } else {
          MessagePlugin.error('保存失败');
        }
      });
    } else {
      llmSourceSave(formData.value).then((res) => {
        if (res) {
          MessagePlugin.success('保存成功');
          onClose();
          emits('confirm');
        } else {
          MessagePlugin.error('保存失败');
        }
      });
    }
  }
};
// const handleTest = () => {
//   MessagePlugin.success('正在开发');
// };
// 监听对话框显示状态
watch(
  () => props.visible,
  (val) => {
    if (val && props.id) {
      paramsId.value = props.id;
      onGetById(props.id);
    } else {
      paramsId.value = '';
    }
  },
  { immediate: true },
);
</script>
<style scoped lang="less">
.form-dome::v-deep {
  .slider-demo {
    width: 95%;
    margin: 0 auto;
  }
  .t-slider__mark-text:last-child {
    width: 24px;
  }
}
</style>
