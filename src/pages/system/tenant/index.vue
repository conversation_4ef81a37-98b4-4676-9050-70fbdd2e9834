<template>
  <div class="api-configuration">
    <t-card class="list-card-container">
      <div class="route-title">
        {{ route.meta.title }}
      </div>
      <div class="search">
        <t-input
          v-model="keywords"
          class="keywords"
          placeholder="输入租户名称、手机号或ID进行搜索"
          autofocus
          clearable
          @clear="onPageChange({ current: 1 })"
        >
          <template #suffixIcon>
            <search-icon />
          </template>
        </t-input>
        <t-button theme="primary" variant="outline" @click="onPageChange({ current: 1 })">搜索</t-button>
      </div>

      <t-table
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="tenantId"
        @page-change="onPageChange"
      >
        <template #tenantId="{ row }">
          {{ row.tenantId?.padStart(7, '0') }}
        </template>
        <template #config="{ row }">
          <div>
            <div>
              合同审查：{{ row.configName }} &nbsp;
              <t-tooltip theme="primary" :duration="3000">
                <template #content>
                  <div>高性能：{{ activeModel?.highSpeedModelName || '-' }}</div>
                  <div>高精度：{{ activeModel?.highQualityModelName || '-' }}</div>
                  <div>审查规则优化：{{ activeModel?.ioModelName || '-' }}</div>
                </template>
                <info-circle-icon
                  style="font-size: 14px; color: var(--td-brand-color)"
                  @mouseenter="onInfoHover(row)"
                />
              </t-tooltip>
            </div>
          </div>
        </template>
        <template #op="{ row }">
          <t-space>
            <t-link v-permission="menuBlockList.includes('EDIT')" theme="primary" hover="color" @click="onEdit(row)">
              配置
            </t-link>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <t-dialog
      header="租户配置"
      :close-on-overlay-click="false"
      :visible="visible"
      width="600px"
      :confirm-btn="{ content: '确定', theme: 'primary', disabled: !rowItem.configId }"
      :cancel-btn="{ content: '取消' }"
      @confirm="onDialogConfirm"
      @close="onClose"
    >
      <t-form colon label-width="80px" label-align="left">
        <t-form-item label="适用配置">
          <t-select v-model="rowItem.configId" clearable filterable placeholder="请选择适用配置" style="width: 300px">
            <t-option v-for="item of tableData2" :key="item.id" :label="item.name" :value="item.id" />
          </t-select>
        </t-form-item>
        <t-form-item label="配置详情">
          <div>
            <div>高性能：{{ activeModel?.highSpeedModelName || '-' }}</div>
            <div>高精度：{{ activeModel?.highQualityModelName || '-' }}</div>
            <div>审查规则优化：{{ activeModel?.ioModelName || '-' }}</div>
          </div>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Tenant',
};
</script>
<script lang="ts" setup>
import { InfoCircleIcon, SearchIcon } from 'tdesign-icons-vue-next';
import type { PrimaryTableCol } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onActivated, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { llmConfigPageByVo, llmTenantConfigPageByVo, llmTenantConfigSave } from '@/api/modules/model';

// 获取当前路由信息
const route = useRoute();
const menuBlockList: string[] = (route.meta?.menuBlockList as string[]) || [];

const visible = ref(false);
interface PaginationState {
  total: number;
  size: number;
  current: number;
}
const loading = ref(false);
const keywords = ref('');
const rowItem = ref<{ [any: string]: any }>({
  configId: '',
  configName: '',
});
const tableData = ref([]);
const tableData2 = ref([]);
const pagination = reactive<PaginationState>({
  total: 0,
  size: 10,
  current: 1,
});
const defaultModel = {
  highSpeedModelName: '',
  highQualityModelName: '',
  ioModelName: '',
};
const idToItemMap = computed(() => {
  const map = new Map();
  tableData2.value.forEach((item) => {
    map.set(item.id, item);
  });
  return map;
});
const activeModel = computed(() => {
  if (!rowItem.value || !rowItem.value.configId || !tableData2.value.length) {
    return defaultModel;
  }
  const item = idToItemMap.value.get(rowItem.value.configId);
  if (item) {
    return {
      highSpeedModelName: item.highSpeedModelName,
      highQualityModelName: item.highQualityModelName,
      ioModelName: item.ioModelName,
    };
  }

  return defaultModel;
});

const columns: PrimaryTableCol[] = [
  { colKey: 'tenantName', title: '租户名称', width: 300, ellipsis: true, fixed: 'left' },
  { colKey: 'tenantId', title: '租户ID', width: 100 },
  { colKey: 'tenantContactPhone', title: '手机号', width: 150 },
  { colKey: 'config', title: '适用配置', width: 200 },
  { colKey: 'updatedTime', title: '最近修改时间', width: 200 },
  { colKey: 'op', title: '操作', width: 80, fixed: 'right' },
];

const onSearch = async () => {
  loading.value = true;
  try {
    await fetchTableData();
  } finally {
    loading.value = false;
  }
};

const onPageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current;
  if (pageInfo.pageSize) pagination.size = pageInfo.pageSize;
  onSearch();
};

const fetchTableData = async () => {
  try {
    const { current, size } = pagination;
    const res = await llmTenantConfigPageByVo({ current, size, tenantName: keywords.value });
    tableData.value = res.records || [];
    pagination.total = Number(res.total) || 0;
  } catch (error) {
    console.error('获取列表失败:', error);
  }
};
const fetchTableData2 = async () => {
  try {
    const res = await llmConfigPageByVo({ current: 1, size: 30 });
    tableData2.value = res.records || [];
  } catch (error) {
    console.error('获取列表失败:', error);
  }
};

const onEdit = (row: any) => {
  rowItem.value = JSON.parse(JSON.stringify(row));
  visible.value = true;
};
const onInfoHover = (row: any) => {
  if (rowItem.value.configId !== row.configId) {
    rowItem.value = row;
  }
};

const onDialogConfirm = () => {
  const params = {
    id: rowItem.value.id,
    tenantId: rowItem.value.tenantId,
    tenantCode: rowItem.value.tenantCode,
    tenantName: rowItem.value.tenantName,
    tenantContactPerson: rowItem.value.tenantContactPerson,
    tenantContactPhone: rowItem.value.tenantContactPhone,
    configId: rowItem.value.configId,
    configName: rowItem.value.configName,
  };
  params.configName = tableData2.value.find((item) => item.id === params.configId)?.name;

  llmTenantConfigSave(params).then((res) => {
    if (!res) {
      MessagePlugin.error('配置失败');
      return;
    }
    MessagePlugin.success('配置成功');
    onClose();
    onSearch();
  });
};
const onClose = () => {
  rowItem.value = {
    configId: '',
    configName: '',
  };
  visible.value = false;
};

onActivated(() => {
  onSearch();
  fetchTableData2();
});
</script>

<style lang="less" scoped>
.api-configuration {
  padding: 0;
  .list-card-container {
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);

    :deep(.t-card__body) {
      padding: 0;
    }
  }
  .route-title {
    height: 22px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: block;
      width: 2px;
      height: 18px;
      background-color: var(--td-brand-color-active);
      margin-right: 8px;
    }
  }
}
.search {
  text-align: right;
  margin-bottom: 12px;
  display: flex;
  .keywords {
    margin-left: auto;
    margin-right: 10px;
    width: 300px;
  }
}
.table-header {
  margin: 10px 0;
  text-align: right;
}
</style>
