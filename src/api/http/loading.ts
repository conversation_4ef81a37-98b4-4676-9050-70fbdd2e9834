/*
 * 全局 Loading 组件
 * @Author: miaozhen<PERSON> <EMAIL>
 * @Date: 2024-04-23 17:03:55
 * @LastEditors: miaozhendong <EMAIL>
 * @LastEditTime: 2024-05-13 14:05:45
 */
import { LOADING_TIME } from '@/constants';

import css from './loading.module.less';

class Loading {
  private counter: number = 0; // 请求计数器

  private timer: NodeJS.Timeout | null = null; // Loading定时器

  // 显示
  show() {
    if (this.counter === 0) {
      clearTimeout(this.counter);
      this.timer = setTimeout(this.open, LOADING_TIME);
    }
    this.counter++;
  }

  // 隐藏
  hide() {
    if (this.counter <= 0) return;
    clearTimeout(this.timer as NodeJS.Timeout);
    this.timer = null;
    this.counter--;
    if (this.counter === 0) {
      this.close();
    }
  }

  // 开启
  open() {
    const loading = document.createElement('div');
    loading.id = 'axiosLoading';
    loading.className = css.loading;
    loading.innerHTML =
      '<span class="loading-bar"><svg class="svg-icon" aria-hidden="true"><use xlink:href="#icon-logo"></use></svg></span>';
    let body = document.querySelector('body');
    if (body) {
      body.classList.add(css.locked);
      body.appendChild(loading);
      body = null;
    }
  }

  // 关闭
  close() {
    let body = document.querySelector('body');
    if (body) {
      body.classList.remove(css.locked);
    }
    body = null;
    let loading = document.querySelector('#axiosLoading');
    if (loading) {
      loading.remove();
    }
    loading = null;
  }
}

export default new Loading();
