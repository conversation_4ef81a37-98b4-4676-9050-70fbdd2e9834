import { fetchEventSource } from '@fortaine/fetch-event-source';
import axios from 'axios';
import { storeToRefs } from 'pinia';

import {
  requestInterception,
  requestInterceptionErr,
  responseInterception,
  responseInterceptionErr,
} from '@/api/http/interceptors';
import { IRequestConfig } from '@/api/types/axios';
import AxiosConfig from '@/constants/axios';
import { useUserStore } from '@/store';
// 创建 axios 实例
const instance = axios.create(AxiosConfig);

// 添加请求拦截器
instance.interceptors.request.use(requestInterception, requestInterceptionErr);

// 添加响应拦截器
instance.interceptors.response.use(responseInterception, responseInterceptionErr);

/**
 * 接口请求公共方法
 * @param params 入参
 * @return {any} 响应数据（泛型）
 */
export function request<T, D>(params: IRequestConfig<D>): Promise<T> {
  // 合并参数
  const config = { ...AxiosConfig, ...params } as IRequestConfig<D>;

  // 读取缓存
  if (config.putCache && config.url) {
    const cacheKey = `coli_${config.url}`;
    const cachedData = sessionStorage.getItem(cacheKey);
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData);
      // 检查缓存是否过期（默认5分钟）
      const cacheExpiration = config.cacheExpiration || 5 * 60 * 1000;
      if (Date.now() - timestamp < cacheExpiration) {
        return Promise.resolve<T>(data);
      }
      // 缓存过期，删除缓存
      sessionStorage.removeItem(cacheKey);
    }
  }
  return instance.request(config);
}

/**
 * 上传公共方法
 * @param params 入参
 * @param options 其他配置
 * @returns {any} 响应数据（泛型）
 */
export function upload<T, D>(params: IRequestConfig<D>, options: IRequestConfig<D> = {}): Promise<T> {
  const config: IRequestConfig<D> = {
    ...params,
    timeout: 1000 * 60 * 60,
    headers: { 'content-type': 'multipart/form-data' },
    ...options,
  };
  return instance.request(config);
}

/**
 * get 请求
 * @param url 请求路径
 * @param data get入参
 * @param options 其他配置
 * @returns {Promise<T>} 响应数据（泛型）
 */
export function get<T, D = any>(url: string, data: D = {} as D, options: IRequestConfig<D> = {}): Promise<T> {
  const config: IRequestConfig<D> = { ...options, url, params: data, method: 'GET' };
  return request<T, D>(config);
}

/**
 * post 请求
 * @param url 请求路径
 * @param data post入参
 * @param options 其他配置
 * @returns {Promise<any>} 响应数据（泛型）
 */
export function post<T, D = any>(url: string, data: D = {} as D, options: IRequestConfig<D> = {}): Promise<T> {
  const config: IRequestConfig<D> = { ...options, url, data, method: 'POST' };
  return request<T, D>(config);
}
// 流式请求
export const stream = (url: string, options: any) => {
  const { body, ...option } = options;
  // 携带Token信息
  const { token } = storeToRefs(useUserStore());
  const headers = {
    'Content-Type': 'application/json',
    [import.meta.env.VITE_TOKEN_NAME]: token.value,
  };
  return fetchEventSource(url, {
    headers,
    method: 'POST',
    openWhenHidden: true,
    body: JSON.stringify(body),
    ...option,
  });
};
