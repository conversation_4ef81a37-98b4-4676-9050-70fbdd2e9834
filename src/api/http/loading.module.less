/*
 * Loading组件样式
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-05-09
 */

.locked {
  overflow: hidden;
}
.loading {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  :global {
    .loading-bar {
      display: inline-block;
      width: 16px;
      height: 16px;
      animation: loading-rotate 1s infinite linear;
      .svg-icon {
        width: 16px;
        height: 16px;
        color: red;
        fill: currentColor;
      }
    }
  }
}
