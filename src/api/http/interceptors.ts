/**
 * Axios 拦截器封装
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/9/5 16:00:37
 */
import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { NotifyPlugin } from 'tdesign-vue-next';

import loading from '@/api/http/loading';
import { IPending, IRequestConfig, IResponseData } from '@/api/types/axios';
import { TOKEN_NAME } from '@/constants';
import { httpErrorCode, messageOptions as msgOptions } from '@/constants/axios';
import Router from '@/router';
import { useUserStore } from '@/store';
import { jumpLogin } from '@/utils/auth';

// 取消重复请求
const pending: Array<IPending> = [];
const { CancelToken } = axios;

/**
 * 重复请求拦截
 */
function removePending<D>(config: InternalAxiosRequestConfig<D>) {
  if (!config.url) return;

  for (const key in pending) {
    const item: number = +key;
    const list: IPending = pending[key];

    if (
      list.url === config.url &&
      list.method === config.method &&
      JSON.stringify(list.params) === JSON.stringify(config.params) &&
      JSON.stringify(list.data) === JSON.stringify(config.data)
    ) {
      // list.cancel?.('操作太频繁，请稍后再试');
      pending.splice(item, 1);
    }
  }
}

/**
 * 请求拦截
 */
export function requestInterception<D>(config: InternalAxiosRequestConfig<D>) {
  const requestConfig = config as unknown as IRequestConfig<D>;

  if (requestConfig.showLoading) {
    loading.hide();
  }

  removePending(config);
  config.cancelToken = new CancelToken((res) => {
    pending.push({
      url: config.url,
      method: config.method,
      params: config.params,
      data: config.data,
      cancel: res,
    });
  });

  const { token } = useUserStore();
  if (config.headers) {
    config.headers[TOKEN_NAME] = `Bearer ${token}`;
  }

  return config;
}

/**
 * 请求异常拦截
 */
export function requestInterceptionErr<T, D>(error: AxiosError<T, D>): Promise<never> {
  return Promise.reject(error);
}

/**
 * 响应拦截
 */
export function responseInterception<T = any, D = any>(response: AxiosResponse<IResponseData<T>>): Promise<any> {
  const config = response.config as unknown as IRequestConfig<D>;
  const data = response.data || {
    resultCode: null,
    errMessage: '请求错误',
    success: false,
    data: null,
  };
  const { resultCode = null, errMessage: message = '请求错误' } = data;
  if (config.showLoading) {
    loading.hide();
  }
  removePending(response.config);

  if (config?.skipIntercept) return Promise.resolve(response.data);

  if (config.isDownLoad) {
    return handleDownloadResponse(response, config);
  }

  const code = String(resultCode);
  if (code !== '0') {
    if (code === '401' || code === '402') {
      jumpLogin();
      return Promise.reject(response);
    }

    if (code === null) return Promise.resolve(response as T);

    NotifyPlugin.warning({ ...msgOptions, content: message });
    return Promise.reject(response);
  }

  if (config.putCache && config.url) {
    sessionStorage.setItem(`coli_${config.url}`, JSON.stringify(data));
  }

  return Promise.resolve(data.data);
}

/**
 * 处理下载响应
 */
function handleDownloadResponse<T, D>(response: AxiosResponse<T>, config: IRequestConfig<D>): Promise<Blob> {
  const content = response.data as unknown as Blob;
  if (!content) {
    NotifyPlugin.warning({ ...msgOptions, content: '下载失败' });
    return Promise.reject(response);
  }

  const blob = new Blob([content], {
    type: response.headers['content-type'] || 'application/octet-stream',
  });
  const fileName = config.downloadOps ? `${config.downloadOps.fileName}.${config.downloadOps.suffix}` : '';

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();

  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);

  return Promise.resolve(blob);
}

/**
 * 响应异常拦截
 */
export function responseInterceptionErr<T, D>(error: AxiosError<T, D>): Promise<never> {
  const config = error.config as unknown as IRequestConfig<D>;
  const { response } = error;
  const status = response ? response.status : (error.code as number | string);

  if (status === 401) {
    const count = Number(sessionStorage.getItem('backLoginCount'));
    if (count >= 2) {
      sessionStorage.removeItem('backLoginCount');
      return Promise.reject(Router.replace('/login'));
    }
    sessionStorage.setItem('backLoginCount', (count + 1).toString());
    return Promise.reject(jumpLogin({ delay: 1500 }));
  }

  const { message = error.message } = httpErrorCode[status] || {};

  if (config?.showLoading) {
    loading.hide();
  }
  NotifyPlugin.warning({ ...msgOptions, content: message });

  return Promise.reject(error);
}
