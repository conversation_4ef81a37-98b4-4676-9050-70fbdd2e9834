/*
 * Axios TypeScript 类型声明文件
 * @Author: miaozhendong <EMAIL>
 * @Date: 2024-05-21 09:43:41
 * @LastEditors: miaozhendong <EMAIL>
 * @LastEditTime: 2024-05-22 17:16:52
 */
import { AxiosRequestConfig, Method } from 'axios';

// 文件下载配置接口
interface IDownloadConfig {
  fileName: string; // 文件名
  suffix: string; // 文件扩展名
}

/**
 * 请求参数接口
 */
export interface IRequestConfig<D = any> extends Omit<AxiosRequestConfig<D>, 'data'> {
  skipIntercept?: any;
  /** 是否开启缓存 */
  putCache?: boolean;
  /** 缓存过期时间（毫秒） */
  cacheExpiration?: number;
  /** 是否显示loading */
  showLoading?: boolean;
  /** 是否显示错误信息 */
  showError?: boolean;
  /** 是否显示成功信息 */
  showSuccess?: boolean;
  /** 错误信息 */
  errorMsg?: string;
  /** 成功信息 */
  successMsg?: string;
  /** 是否重试 */
  retry?: boolean;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟 */
  retryDelay?: number;
  /** 请求数据 */
  data?: D | string;
  /** 是否下载 */
  isDownLoad?: boolean;
  /** 下载配置 */
  downloadOps?: IDownloadConfig;
}

/**
 * 响应数据接口
 */
export interface IResponseData<T = any> {
  resultCode: string; // 状态码
  errMessage: string; // 消息
  success: boolean; // 状态
  data: T; // 数据
  [propName: string]: any; // 任意属性
}

/**
 * 待取消请求接口
 */
export interface IPending<T = any, D = any> {
  url?: string;
  method?: Method | string;
  params: T;
  data: D;
  cancel: (text: string) => void;
}
