/**
 * 用户 TypeScript 类型声明文件
 * Author: yangxianyue <EMAIL>
 * Date: 2022/10/9 16:40:14
 * LastEditors: miaozhendong <EMAIL>
 * LastEditTime: 2023-08-16 16:46:15
 */

/**
 * 用户列表请求参数接口
 */
export interface IUserParams {
  page?: number; // 页码
  pageSize?: number; // 每页条目数
  fullName?: string; // 用户姓名
  userName?: string; // 用户账号
  employeeType?: string; // 用户类型
  personType?: string; // 人员类型
  status?: string; // 状态
  guid?: string;
  // status: number | string | null, // 状态
}
export interface IUserForm {
  guid?: string; // 用户编码
  deptGuid?: string;
  id?: string;
  userName: string; // 用户账号
  fullName: string; // 用户姓名
  personType: string | number; // 人员类型
  userPassWord?: string; // 初始密码
  supplierId?: string; // 所属公司
  supplierName?: string; // 所属公司
  deptName: string; // 部门
  sex: string; // 性别
  email: string; // 电子邮箱
  status: string; // 状态
}

/**
 * 登录请求参数接口
 */
export interface ILoginParams {
  clientIp: string; // 客户端ip地址
  username: string; // 用户名
  password: string; // 密码
}

/**
 * 登录响应数据接口
 */
export interface ILoginData {
  token: string; // Token
  userInfo: AuthInfo; // 用户信息
}

/**
 * 用户数据接口
 */
export interface IUserData {
  token?: string; // token
  fullName: string; // 用户名
  userAccount: string; // 账号
  guid: string; // 主键
  avatar?: string; // 头像
  [propName: string]: any; // 任意属性
}

/**
 * 用户列表响应参数接口
 */
export interface IUserResult {
  records: IUserData[]; // 列表数据
  total: string; // 条目数
  [key: string]: any; // 其他属性
}

// 登录信息接口
export interface AuthInfo {
  id?: string;
  guid?: string;
  userName?: string;
  fullName?: string;
  nickName?: string;
  orgGuid?: string;
  positionName?: string | null;
  orgName?: string | null;
  avatarUrl?: string;
  sex?: number;
  email?: string;
  mobile?: string;
  sysOrgInfoVo?: any;
  token?: string;
  refreshToken?: string;
  [property: string]: any;
}
