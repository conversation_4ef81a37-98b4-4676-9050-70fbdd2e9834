/**
 * Author: yangxianyue <EMAIL>
 * Date: 2023-03-14 10:51:42
 */

/**
 * 菜单列表请求参数接口
 */
export interface IMenuParams {
  page?: number; // 页码
  pageSize?: number; // 每页条目数
  menuName?: string;
  url?: string;
  // status: number | string | null, // 状态
}

export interface IMenuButton {
  blockName: string;// 用户编码
  blockKey: string;
  remark?: string; // 用户账号
  menuId?: string;
}

/**
 * 菜单列表响应参数接口
 */
export interface IMenuResult {
  records: IMenuData[]; // 列表数据
  total: string; // 条目数
  [key: string]: any; // 其他属性
}

/**
 * 菜单数据接口
 */
export interface IMenuData {
  id: string; // ID
  menuName: string;
  menuType: string; // 字典键
  url: string;
  level: string; // 字典值
  status: string;
  [key: string]: any; // 其他属性
}

export interface IMenuForm {
  id?: string;
  menuName?: string;
  menuType?: string;
  parentId?: string | number;
  parentName?: string;
  icon?: string;
  orderNum?: string;
  status?: string | number;
  level?: number;
  props?: string;
  url?: string;
  remark?: string;
  menuBlockVos?: IMenuButton[];
}

export interface IMeta {
  title: string;
  icon?: string;
  [key: string]: any;
}

// 路由数据接口
export interface IRoute {
  path: string;
  name?: string;
  redirect?: string;
  hidden?: boolean;
  meta?: IMeta,
  component?: string;
  children?: IRoute[];
}