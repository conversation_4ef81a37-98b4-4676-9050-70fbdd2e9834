/**
 * 部门模块 TypeScript 类型声明文件
 * Author: yangxianyue <EMAIL>
 * Date: 2022/10/9 16:40:14
 * LastEditors: yangxianyue <EMAIL>
 * LastEditTime: 2023-03-17 10:35:19
 */

/**
 *角色列表请求参数接口
 */
export interface IRoleParams {
  page?: number; // 页码
  pageSize?: number; // 每页条目数
  deptGuid?: string | null; // 组织
  roleName?: string; // 角色名称
  type?: string | null; // 角色类型
  roleCode?: string; // 角色编码
  status?: string; // 状态
  levelId?: string | null;
  functionalLineId?: string | null;
  levelName?: string | null; // 层级
  functionalLineName?: string | null; // 业务线
  // status: number | string | null, // 状态
}

/**
 * 角色列表响应参数接口
 */
export interface IRoleResult {
  records: IRoleData[]; // 列表数据
  total: string; // 条目数
  [key: string]: any; // 其他属性
}

/**
 * 角色数据接口
 */
export interface IRoleData {
  id?: string; // ID
  deptName?: string; // 组织名称
  roleName?: string; // 角色名称
  roleCode?: string; // 角色编码
  levelName?: string; // 层级
  functionalLineName?: string; // 业务线
  dataDimensions?: string[] | null; // 数据权限
  resourceRoleList?: string[] | null;
  type?: string; // 角色类型
  standardRoleId?: string;
  remark?: string; // 备注
  status?: string; // 状态
  [key: string]: any; // 其他属性
}

/*
角色表单
*/
export interface IRoleForm {
  id?: string;
  roleCode?: string; // 角色编码
  roleName?: string; // 角色名称
  type?: string; // 角色类型
  status?: string | number; // 状态
  remark?: string; // 备注
  deptGuid?: string; // 组织
  dataDimensions?: string[]; // 数据范围
  resourceRoleList?: IResourceRole[]; // 功能权限
  standardRoleId?: string;
}
/*
标准角色参数
*/
export interface IStandRoleParams {
  name?: string;
  page?: number;
  pageSize?: number;
}
/*
 角色权限
*/
export interface IRolePermissionAdd {
  resourceId?: string;
  resourceType?: string;
  resourceName?: string;
  enableView?: number;
  roleId?: string;
}

export interface IMenuAuthTree extends IResourceRole {
  childrenList: IMenuAuthTree[];
  hasChild: boolean;
}
export interface IResourceRole {
  enableView: number;
  resourceId: string;
  resourceName: string;
  resourceType: number;
  [key: string]: any; // 其他属性
}

export interface IResultRecords<T> {
  records: T;
}
export interface IStandardRoleData {
  id: string;
  code: string;
  createPerson: string;
  createTime: number | null;
  deptGuid: string;
  deptName: string;
  functionalLineId: string;
  functionalLineName: string;
  levelId: string;
  levelName: string;
  name: string;
  parentId: string;
  remark: string;
  status: number;
  type: string;
  updatePerson: string;
}
