/**
 * 字典模块 TypeScript 类型声明文件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/10/9 16:40:14
 */

/**
 * 字典列表请求参数接口
 */
export interface IDictParams {
  page?: number, // 页码
  pageSize?: number, // 每页条目数
  dictKey?: string, // 字典键
  dictValue?: string, // 字典值
  // status: number | string | null, // 状态
}


/**
 * 字典列表响应参数接口
 */
export interface IDictResult {
  records: IDictData[]; // 列表数据
  total: string; // 条目数
  [key: string]: any; // 其他属性
}

/**
 * 字典数据接口
 */
export interface IDictData {
  id?: string; // ID
  parentId?:string|number;
  dictKey: string; // 字典键
  fullKey?:string;
  dictValue: string; // 字典值
  description?:string;
  status: number; // 状态
  [key: string]: any; // 其他属性
}

export interface DictMapParams {
  dictKey:string;
  dictValue:string;
  children?:Array<DictMapParams>
}
export interface DictMapResult{
  [dictKey:string]:string|{
    [dictKey:string]:string
  }
}