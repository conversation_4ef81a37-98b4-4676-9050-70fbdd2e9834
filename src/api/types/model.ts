export interface llmConfigObject {
  /**
   * 规则ID
   */
  code?: string;
  /**
   * 高精度模型ID
   */
  highQualityModelId: string;
  /**
   * 高精度模型名称
   */
  highQualityModelName: string;
  /**
   * 高性能模型ID
   */
  highSpeedModelId: string;
  /**
   * 高性能模型名称
   */
  highSpeedModelName: string;
  /**
   * 规则名称
   */
  name: string;
  /**
   * 备注说明
   */
  remark: string;
  /**
   * 状态 1启用；0禁用，暂时不需要传，冗余留备的
   */
  status?: number;
  [key: string]: any;
}

export interface ModelObject {
  /**
   * 模型API基础地址
   */
  apiBaseUrl: string;
  /**
   * 模型API密钥
   */
  apiKey: string;
  /**
   * 模型接口协议
   */
  apiMethod: string;
  /**
   * API版本号（如适用）
   */
  apiVersion?: string;
  /**
   * 是否启用
   */
  enable: number;
  /**
   * 频率惩罚参数
   */
  frequencyPenalty?: number;
  /**
   * 最大token数
   */
  maxTokens?: number;
  /**
   * 模型名称，例如：gpt-4-turbo、qwen-max
   */
  modelName: string;
  /**
   * 存在惩罚参数
   */
  presencePenalty?: number;
  /**
   * 生成文本的随机程度
   */
  temperature?: number;
  /**
   * 请求超时时间（秒）
   */
  timeout: number;
  /**
   * 核采样概率
   */
  topP?: number;
  [property: string]: any;
}
