/**
 * @interface ReviewRequest
 * @description 新建任务请求参数接口
 */
export interface ReviewRequest {
  /**
   * 文件ID
   * @description 要审查的文件唯一标识
   */
  fileId: string;

  /**
   * 文件名称
   * @description 要审查的文件名称
   */
  fileName: string;

  /**
   * 包含的规则数量（字符串类型）
   * @description 表示包含的审查规则数量，当前为字符串格式
   */
  includedRules: string;

  /**
   * 审查类型
   * @description 当前审查类型，例如 REVIEW
   */
  type?: string;

  /**
   * 审查规则清单ID
   * @description 指定使用的审查规则清单的唯一标识
   */
  reviewRuleListId: string;
  reviewRuleListName: string;

  /**
   * 审查模式
   * @description 审查的速度模式：HIGH_SPEED-高速、PRECISION-精准等
   */
  reviewMode: string;

  /**
   * 审查页面范围
   * @description 指定审查的页面范围，如 ALL-全部页面
   */
  reviewPageRange: string;

  /**
   * 模型ID
   * @description 使用的审查模型的唯一标识
   */
  modelId?: number | string;
}

/**
 * @interface TaskItem
 * @description 任务信息接口，用于表示系统中一个审查任务的基本属性和状态。
 */
export interface TaskItem {
  /**
   * 任务编号
   */
  code: string;

  /**
   * 创建人ID
   */
  createdBy: string;

  /**
   * 创建人名称
   */
  createdName: string;

  /**
   * 创建时间（ISO格式）
   */
  createdTime: string;

  /**
   * 删除标记，0未删除，1已删除
   */
  deleted: number | string;

  /**
   * 错误码（任务执行失败时存在）
   */
  errorCode: string;

  /**
   * 错误信息（任务执行失败时存在）
   */
  errorMessage: string;

  /**
   * 排除的规则数量（字符串类型）
   */
  excludedRules: string;

  /**
   * 文件处理耗时（单位：毫秒）
   */
  fileDuration: number | string;

  /**
   * 文件唯一标识（数字ID）
   */
  fileId: number | string;

  /**
   * 文件名称
   */
  fileName: string;

  /**
   * 任务唯一标识（数字ID）
   */
  id: number | string;

  /**
   * 包含的规则数量（字符串类型）
   */
  includedRules: string;

  /**
   * LLM处理耗时（单位：毫秒）
   */
  llmDuration: number;

  /**
   * 使用的模型ID（字符串格式）
   */
  modelId: string;

  /**
   * 模型名称
   */
  modelName: string;

  /**
   * 审查模式，如 HIGH_SPEED-高性能、HIGH_QUALITY-高精度
   */
  reviewMode: 'HIGH_SPEED' | 'HIGH_QUALITY';

  /**
   * 审查页面范围，如 ALL-全部页面
   */
  reviewPageRange: string;

  /**
   * 审查规则清单ID
   */
  reviewRuleListId: string;

  /**
   * 任务当前状态
   */
  status: string;

  /**
   * 租户ID
   */
  tenantId: number | string;

  /**
   * 总耗时（单位：毫秒）
   */
  totalDuration: number | string;

  /**
   * 任务类型，如 REVIEW 等
   */
  type: string;

  /**
   * 最后更新人ID
   */
  updatedBy: string;

  /**
   * 最后更新人名称
   */
  updatedName: string;

  /**
   * 最后更新时间（ISO格式）
   */
  updatedTime: string;

  /**
   * 用户ID
   */
  userId: string;

  /**
   * 支持扩展字段
   */
  [property: string]: any;
}

/**
 * @interface SaveSchema
 * @description 保存任务配置的数据结构定义
 */
export interface SaveSchema {
  /**
   * 文件ID
   */
  fileId: string;

  /**
   * 文件名称
   */
  fileName: string;

  /**
   * 包含的规则，支持字符串或数组形式
   */
  includedRules: Array<string>;

  /**
   * 使用的模型ID（可选）
   */
  modelId?: number;

  /**
   * 审查模式，如 HIGH_SPEED-高性能、HIGH_QUALITY-高精度
   */
  reviewMode: 'HIGH_SPEED' | 'HIGH_QUALITY';

  /**
   * 审查页面范围，如 ALL-全部页面
   */
  reviewPageRange: string;
  reviewRuleListName: string;
  /**
   * 审查规则清单ID
   */
  reviewRuleListId: string;

  /**
   * 任务类型（可选）
   */
  type?: string;

  /**
   * 支持扩展字段
   */
  [property: string]: any;
}
/**
 * @interface ReviewResult
 * @description 审查结果数据结构接口
 */
export interface MessageProps {
  /**
   * 租户id
   */
  tenantId: number | string;

  /**
   * 用户id
   */
  userId: string;

  /**
   * 关联任务ID
   */
  taskId: number | string;

  /**
   * 关联文件ID
   */
  fileId: number | string;

  /**
   * 审查规则ID
   */
  reviewRuleId: number | string;

  /**
   * 审查规则名称
   */
  reviewRuleName: string;

  /**
   * 使用的大模型Id
   */
  modelId: number | string;

  /**
   * 使用的大模型名称
   */
  modelName: string;

  /**
   * 审查结果：PASSED-通过，FAILED-有风险
   */
  result: 'PASSED' | 'FAILED';

  /**
   * NONE-无风险、LOW-低风险、MEDIUM-中风险、HIGH-高风险
   */
  riskLevel: 'NONE' | 'LOW' | 'MEDIUM' | 'HIGH';

  /**
   * 审查状态：REVIEWING-审查中，COMPLETED-已完成，FAILED-失败
   */
  status: 'REVIEWING' | 'COMPLETED' | 'FAILED';

  /**
   * 审查备注或说明
   */
  comments: string;
  /**
   * 支持扩展字段
   */
  [property: string]: any;
}
export type sseKey = 'reconnectSse' | 'retryReview' | 'reviewById';
