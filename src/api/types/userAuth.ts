/**
 * Author: yangxianyue <EMAIL>
 * Date: 2023-03-17 11:44:53
 * LastEditors: yangxianyue <EMAIL>
 * LastEditTime: 2023-03-20 14:01:20
 */

/**
 * 用户权限列表请求参数接口
 */
export interface IUserAuthParams {
  page?: number; // 页码
  pageSize?: number; // 每页条目数
  employeeName?: string; // 用户姓名
  roleId?: string | null; // 用户类型
}

/**
 * 用户权限列表响应参数接口
 */
export interface IUserAuthResult {
  records: IUserAuthData[]; // 列表数据
  total: string; // 条目数
  [key: string]: any; // 其他属性
}

/**
 *用户权限数据接口
 */
export interface IUserAuthData {
  id?: string; // ID
  employeeName?: string; // 姓名
  userName?: string; // 账号
  roleType?: string; // 角色类型
  roleName?: string; // 角色名称
  remark?: string; // 备注信息
  [key: string]: any; // 其他属性
}

export interface IUserAuthForm {
  id?: string;
  employeeGuid?: string;
  employeeName?: string; // 姓名
  deptGuid?: string;
  deptName?: string;
  userName?: string; // 账号信息
  roleId?: string; // 角色名称
  roleName?: string;
  remark?: string; // 备注信息
  orgArea?: string[];
}
