/**
 * 选择器 TypeScript 类型声明文件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/12/29 11:20:47
 * LastEditors: mi<PERSON><PERSON>@live.com
 * LastEditTime: 2022/12/29 11:20:47
 */
import { IUserData } from '@/api/types/user';

export interface IQueryOrgTreeParams {
  guid: string;
  deptName?: string;
  treeLevel?: string;
}

export interface IOrgTreeData {
  key: string;
  id: string;
  guid: string;
  deptName: string;
  level: number;
  orderNum: number;
  parentGuid: string;
  parentId: string;
  status: number;
  hasChild?: boolean;
  childrenList?: IOrgTreeData[];
  [propName: string]: any; // 额外属性
}

export interface IQueryOrgTreeData {
  records: IOrgTreeData[];
}

export interface IQueryUserListParams {
  deptId: string;
  page: number;
  pageSize: number;
}

export interface IQueryUserData {
  records: IUserData[];
  [propName: string]: any; // 额外属性
}
