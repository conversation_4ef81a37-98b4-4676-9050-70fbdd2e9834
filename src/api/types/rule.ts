/**
 * 规则管理模块类型定义
 * 包含规则清单、审查项、示例等相关接口类型
 */

/**
 * 规则清单保存请求参数
 * @interface SaveRequest
 * @description 用于创建新的规则清单时的请求参数
 */
export interface SaveRequest {
  /**
   * 审查清单描述
   * @description 对规则清单的详细说明
   */
  description?: string;
  /**
   * 审查清单名称
   * @description 规则清单的标题名称
   */
  name: string;
  [property: string]: any;
}

/**
 * 通用响应类型
 * @interface Response
 * @description API 接口的统一响应格式
 */
export interface Response {
  /**
   * 响应数据
   * @description 接口返回的具体数据内容
   */
  date: boolean | Object | null | undefined;
  /**
   * 响应状态码
   * @description 接口调用的状态码
   */
  resultCode: number;
  /**
   * 是否成功
   * @description 标识接口调用是否成功
   */
  success: boolean;
  [property: string]: any;
}

/**
 * 规则清单更新请求参数
 * @interface UpdateByIdRequest
 * @description 用于更新现有规则清单的请求参数
 */
export interface UpdateByIdRequest {
  /**
   * 审查清单描述
   * @description 对规则清单的详细说明
   */
  description?: string;
  /**
   * 规则清单ID
   * @description 要更新的规则清单的唯一标识
   */
  id: number | string;
  /**
   * 审查清单名称
   * @description 规则清单的标题名称
   */
  name: string;
  /**
   * 审查项列表
   * @description 规则清单中包含的所有审查项
   */
  ruleVos?: RuleVo[];
  /**
   * 共享范围
   * @description 规则清单的共享范围：GENERAL-通用、TENANT_SHARED-租户共享、PRIVATE-私有
   */
  shareScope?: string;
  /**
   * 来源类型
   * @description 规则清单的来源：SYSTEM-系统内置，CUSTOM-客户自定义
   */
  sourceType?: string;
  /**
   * 租户ID
   * @description 所属租户的唯一标识
   */
  tenantId?: number | string;
  /**
   * 用户ID
   * @description 创建或修改用户的唯一标识
   */
  userId?: string;
  [property: string]: any;
}

/**
 * 规则项数据
 * @interface RuleVo
 * @description 单个审查规则项的详细信息
 */
export interface RuleVo {
  /**
   * 标准编码
   * @description 由系统自动生成的唯一编码
   */
  code?: string;
  /**
   * 规则项ID
   * @description 规则项的唯一标识
   */
  id?: number | string;
  /**
   * 规则名称
   * @description 规则项的标题名称
   */
  name: string;
  /**
   * 关联关系ID
   * @description 与其他实体的关联关系标识
   */
  relationId?: number | string;
  /**
   * 所属清单ID
   * @description 规则项所属的规则清单ID
   */
  ruleListId: number | string;
  /**
   * 风险等级
   * @description 规则的风险等级：LOW-低风险、MEDIUM-中风险、HIGH-高风险
   */
  riskLevel?: string;
  /**
   * 审查规则
   * @description 具体的审查规则内容
   */
  rule: string;
  /**
   * 规则来源
   * @description 规则的生成方式：LLM-大语言模型生成、MANUAL-人工编写
   */
  ruleSource?: string;
  /**
   * 租户ID
   * @description 所属租户的唯一标识
   */
  tenantId?: number | string;
  /**
   * 用户ID
   * @description 创建或修改用户的唯一标识
   */
  userId?: string;
  [property: string]: any;
}

/**
 * 分页查询参数
 * @interface PageByVo
 * @description 用于分页查询规则清单的参数
 */
export interface PageByVo {
  /**
   * 当前页码
   * @description 要查询的页码，从1开始
   */
  current: number;
  /**
   * 审查清单名称
   * @description 用于按名称筛选规则清单
   */
  name?: string;
  /**
   * 共享范围
   * @description 按共享范围筛选：GENERAL-通用、TENANT_SHARED-租户共享、PRIVATE-私有
   */
  shareScope?: string;
  /**
   * 每页数量
   * @description 每页显示的记录数
   */
  size: number;
  /**
   * 来源类型
   * @description 按来源类型筛选：SYSTEM-系统内置，CUSTOM-客户自定义
   */
  sourceType?: string;
  [property: string]: any;
}

/**
 * 审查项更新请求参数
 * @interface ReviewRuleUpdateById
 * @description 用于更新单个审查项的请求参数
 */
export interface ReviewRuleUpdateById {
  /**
   * 标准编码
   * @description 由系统自动生成的唯一编码
   */
  code: string;
  /**
   * 审查示例列表
   * @description 与该审查项相关的示例列表
   */
  exampleList?: ExampleList[];
  /**
   * 审查项ID
   * @description 要更新的审查项的唯一标识
   */
  id: number | string;
  /**
   * 规则名称
   * @description 审查项的标题名称
   */
  name: string;
  /**
   * 关联关系ID
   * @description 与其他实体的关联关系标识
   */
  relationId: number | string;
  /**
   * 所属清单ID
   * @description 审查项所属的规则清单ID
   */
  ruleListId: number | string;
  /**
   * 风险等级
   * @description 规则的风险等级：LOW-低风险、MEDIUM-中风险、HIGH-高风险
   */
  riskLevel: string;
  /**
   * 审查规则
   * @description 具体的审查规则内容
   */
  rule: string;
  /**
   * 规则来源
   * @description 规则的生成方式：LLM-大语言模型生成、MANUAL-人工编写
   */
  ruleSource: string;
  /**
   * 租户ID
   * @description 所属租户的唯一标识
   */
  tenantId: number | string;
  /**
   * 用户ID
   * @description 创建或修改用户的唯一标识
   */
  userId: string;
  [property: string]: any;
}

/**
 * 审查示例数据
 * @interface ExampleList
 * @description 审查规则的示例数据
 */
export interface ExampleList {
  /**
   * 合同内容
   * @description 示例中的合同文本内容
   */
  contractContent: string;
  /**
   * 正确结果
   * @description 示例的预期结果：NONE-无风险、RISK-存在风险
   */
  correctResult: string;
  /**
   * 示例ID
   * @description 示例的唯一标识
   */
  id?: number | string;
  /**
   * 审查说明
   * @description 对示例的审查结果说明
   */
  reviewDescription: string;
  /**
   * 关联的审查规则ID
   * @description 示例所属的审查规则ID
   */
  reviewRuleId: number | string;
  /**
   * 规则来源
   * @description 示例的来源：FEEDBACK-用户反馈，BUILTIN-内置
   */
  ruleOrigin: string;
  /**
   * 租户ID
   * @description 所属租户的唯一标识
   */
  tenantId?: number | string;
  /**
   * 用户ID
   * @description 创建或修改用户的唯一标识
   */
  userId?: number | string;
  [property: string]: any;
}

/**
 * 审查示例更新请求参数
 * @interface ExampleUpdateById
 * @description 用于更新审查示例的请求参数
 */
export interface ExampleUpdateById {
  /**
   * 合同内容
   * @description 示例中的合同文本内容
   */
  contractContent: string;
  /**
   * 正确结果
   * @description 示例的预期结果：NONE-无风险、RISK-存在风险
   */
  correctResult: string;
  /**
   * ID
   * @description 要更新的示例的唯一标识
   */
  id: number | string;
  /**
   * 审查说明
   * @description 对示例的审查结果说明
   */
  reviewDescription: string;
  /**
   * 关联的审查规则ID
   * @description 示例所属的审查规则ID
   */
  reviewRuleId: number | string;
  /**
   * 规则来源
   * @description 示例的来源：FEEDBACK-用户反馈，BUILTIN-内置
   */
  ruleOrigin?: string;
  /**
   * 租户ID
   * @description 所属租户的唯一标识
   */
  tenantId?: number | string;
  /**
   * 用户ID
   * @description 创建或修改用户的唯一标识
   */
  userId?: number | string;
  [property: string]: any;
}

/**
 * 审查示例反馈请求参数
 * @interface ExampleFeedback
 * @description 用于提交审查示例反馈的请求参数
 */
export interface ExampleFeedback {
  /**
   * 合同内容
   * @description 示例中的合同文本内容
   */
  contractContent: string;
  /**
   * 正确结果
   * @description 示例的预期结果：NONE-无风险、RISK-存在风险
   */
  correctResult: 'NONE' | 'RISK';
  /**
   * ID
   * @description 示例的唯一标识
   */
  id?: number;
  /**
   * 审查说明
   * @description 对示例的审查结果说明
   */
  reviewDescription: string;
  /**
   * 关联的审查规则ID
   * @description 示例所属的审查规则ID
   */
  reviewRuleId: string;
  /**
   * 租户ID
   * @description 所属租户的唯一标识
   */
  tenantId?: number;
  /**
   * 用户ID
   * @description 创建或修改用户的唯一标识
   */
  userId?: number;
  [property: string]: any;
}
