/**
 * 部门模块 TypeScript 类型声明文件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/10/9 16:40:14
 * LastEditors: miaozhendong <EMAIL>
 * LastEditTime: 2023-08-16 17:13:32
 */

// 部门列表请求参数接口
export interface IDeptParams {
  page?: number; // 页码
  pageSize?: number; // 每页条目数
  deptName?: string; // 部门名称
  status: number | string | null; // 状态
  guid: string; // 起始部门节点guid
}

// 部门列表响应参数接口
export interface IDeptResult {
  records: IDeptData[]; // 列表数据
  total: string; // 条目数
  [key: string]: any; // 其他属性
}

// 部门数据接口
export interface IDeptData {
  id?: string; // ID
  guid?: string; // Guid
  deptName: string; // 名称
  deptStaffNum?: number; // 员工数
  status?: number | string; // 状态
  parentGuid?: string; // 父级Guid
  parentDeptName?: string; // 父级名称
  leaderGuid?: string; // 主管Guid
  leaderUserName?: string; // 主管账号
  leaderName?: string; // 主管名称
  level?: number | null; // 层级
  children?: IDeptData[]; // 子部门
  [key: string]: any; // 其他属性
}
