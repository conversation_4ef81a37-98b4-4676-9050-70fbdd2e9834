/**
 * 规则管理模块 API
 * 包含规则清单和审查项的相关接口
 */

import { FetchEventSourceInit } from '@fortaine/fetch-event-source';

import { get, post, stream } from '@/api/http';
import { IRequestConfig, IResponseData } from '@/api/types/axios';

import { ReviewRequest, sseKey } from '../types/task';
/**
 * 请求配置
 * @type {IRequestConfig}
 */
const options: IRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL_PREFIX,
};

// 任务列表分页
export function taskPageByVo(params: { current: number; size: number }) {
  return post<IResponseData>(`/task/pageByVo`, params, options);
}
// 查询指定任务
export function taskGetById(id: number | string) {
  return get<IResponseData>(`/task/getById/${id}`, {}, { ...options, showLoading: false });
}
// 删除指定任务
export function taskDeleteById(id: number | string) {
  return get<IResponseData>(`/task/deleteById/${id}`, {}, options);
}
// 终止任务
export function taskTerminatedById(id: number | string) {
  return get<IResponseData>(`/task/terminatedById/${id}`, {}, options);
}
// 新建任务
export function taskSave(key: sseKey, params: ReviewRequest | string, option: FetchEventSourceInit) {
  const baseUrl = import.meta.env.VITE_API_URL_PREFIX;
  if (key) {
    return stream(`${baseUrl}/task/${key}/${params}`, {
      method: 'GET',
      ...option,
    });
  }
  return stream(`${baseUrl}/task/save`, {
    body: params,
    ...option,
  });
}
// 重试任务
export function reviewById(id: string) {
  return get<IResponseData>(`/task/reviewById/${id}`, {}, options);
}
// 重试审查项
export function singleRuleReview(id: string) {
  return get<IResponseData>(`/task/singleRuleReview/${id}`, {}, options);
}
// 新建任务
export function taskSavePost(params: any) {
  return post<IResponseData>(`/task/save`, params, options);
}
// 查询未完成列表状态更新
export function queryStatus(params: any) {
  return post<IResponseData>(`/task/queryStatus`, params, options);
}
// 重试审查任务
export function retryById(id: number | string) {
  return get<IResponseData>(`/task/retryById/${id}`, {}, options);
}
// 上传文件
export function taskUpload(params: any) {
  return post<IResponseData>(`/taskFiles/upload`, params, options);
}
// 主动关闭sse连接
export function closeSseConnect(id: number | string) {
  return get<IResponseData>(`/task/closeSseConnect/${id}`, {}, options);
}
// 获取WPS预览接口
export function getPreviewUrl(fileId: number | string) {
  return get<IResponseData>(`/wps/getPreviewUrl/${fileId}`, {}, options);
}
// 获取WPS编辑接口
export function getEditUrl(fileId: number | string) {
  return get<IResponseData>(`/wps/getEditUrl/${fileId}`, {}, options);
}
// 获取文件正文
export function fileContent(fileId: number | string) {
  return get<IResponseData>(`/taskFiles/fileContent/${fileId}`, {}, options);
}
// 校验用户是否存在进行中任务
export function hasReviewingTask() {
  return get<IResponseData>(`/task/hasReviewingTask`, {}, options);
}

export function exportExcel(id: number | string, name: string) {
  return get<IResponseData>(
    `/task/exportExcel/${id}`,
    {},
    { ...options, isDownLoad: true, responseType: 'blob', downloadOps: { fileName: name, suffix: 'xlsx' } },
  );
}
