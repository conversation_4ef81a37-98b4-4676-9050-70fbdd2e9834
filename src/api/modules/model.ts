/**
 * 模型 API
 */

import { get, post } from '@/api/http';
import { IRequestConfig, IResponseData } from '@/api/types/axios';
/**
 * 请求配置
 * @type {IRequestConfig}
 */
const options: IRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL_PREFIX,
};

// 配置列表分页
export function llmConfigPageByVo(params: { current: number; size: number }) {
  return post<IResponseData>(`/llmConfig/pageByVo`, params, options);
}
// 大模型配置列表
export function llmConfigListByVo(params: any) {
  return post<IResponseData>(`/llmConfig/listByVo`, params, options);
}
// 新增大模型配置
export function llmConfigSave(params: any) {
  return post<IResponseData>(`/llmConfig/save`, params, options);
}
// 更新大模型配置
export function llmConfigUpdateById(params: any) {
  return post<IResponseData>(`/llmConfig/updateById`, params, options);
}
// 查询单个大模型配置
export function llmConfigGetById(id: number | string) {
  return get<IResponseData>(`/llmConfig/getById/${id}`, {}, { ...options, showLoading: false });
}
// 删除大模型配置
export function llmConfigDeleteById(id: number | string) {
  return get<IResponseData>(`/llmConfig/deleteById/${id}`, {}, options);
}

// 分页查询大模型资源
export function llmSourcePageByVo(params: { current: number; size: number }) {
  return post<IResponseData>(`/llmSource/pageByVo`, params, options);
}
// 新建大模型资源
export function llmSourceSave(params: any) {
  return post<IResponseData>(`/llmSource/save`, params, options);
}
// 更新大模型资源
export function llmSourceUpdateById(params: any) {
  return post<IResponseData>(`/llmSource/updateById`, params, options);
}
// 查询单个大模型配置
export function llmSourceGetById(id: number | string) {
  return get<IResponseData>(`/llmSource/getById/${id}`, {}, { ...options, showLoading: false });
}
// 删除大模型配置
export function llmSourceDeleteById(id: number | string) {
  return get<IResponseData>(`/llmSource/deleteById/${id}`, {}, options);
}

// 租户大模型配置规则分页列表
export function llmTenantConfigPageByVo(params: any) {
  return post<IResponseData>(`/llmTenantConfig/pageByVo`, params, options);
}
// 调整租户的大模型配置
export function llmTenantConfigSave(params: any) {
  return post<IResponseData>(`/llmTenantConfig/save`, params, options);
}
