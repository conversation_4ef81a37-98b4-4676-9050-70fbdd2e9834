import { get, post } from '@/api/http';
import { IRequestConfig, IResponseData } from '@/api/types/axios';

/**
 * 请求配置
 * @type {IRequestConfig}
 */
const options: IRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL_PREFIX,
};
// 列表分页
export function pageByVo(params: { current: number; size: number }) {
  return post<IResponseData>(`/apiConfig/pageByVo`, params, options);
}
// 更新API配置
export function updateById(params: any) {
  return post<IResponseData>(`/apiConfig/updateById`, params, options);
}
// 列表分页
export function save(params: any) {
  return post<IResponseData>(`/apiConfig/save`, params, options);
}
// 查询单个API配置
export function getById(id: string) {
  return get<IResponseData>(`/apiConfig/getById/${id}`, {}, options);
}
// 删除API配置
export function deleteById(id: string) {
  return get<IResponseData>(`/apiConfig/deleteById/${id}`, {}, options);
}
