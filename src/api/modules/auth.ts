import { get, post } from '@/api/http';
import { IRequestConfig } from '@/api/types/axios';
import { AuthInfo, ILoginParams } from '@/api/types/user';

interface SSOConfigResponse {
  redirectFullUrl?: string;
}

const options: IRequestConfig = {
  baseURL: `${import.meta.env.VITE_API_URL}/iam`,
};

/**
 * 登录
 * @param params 用户名
 * @returns {Promise<ILoginData>}
 */
export function login(params: ILoginParams) {
  return post<any>('/tenant/login', params, options);
}

export function getUserInfo() {
  return get<any>('/user/getUserInfo', {}, { baseURL: `${import.meta.env.VITE_API_URL}/manage` });
}

/**
 * 获取SSO配置
 * @param appCode 应用代码
 * @param state 状态
 * @returns {Promise<SSOConfigResponse>}
 * @throws {AxiosError} 当请求失败时抛出错误
 */
export function getSSOConfig(appCode: string, state: string): Promise<SSOConfigResponse> {
  return get<SSOConfigResponse>(`/provider/config/provider/${appCode}`, { providerCode: state }, options);
}

/**
 * SSO登录
 * @param appCode 应用代码
 * @param state 状态参数
 * @param code 授权码
 * @returns {Promise<any>} 返回登录结果
 */
export async function ssoLogin(appCode: string, state: string, code: string) {
  const { token, refreshToken, ...userInfo } = await get<AuthInfo>(
    `/provider/login/${state}/${appCode}`,
    { code },
    options,
  );
  return { token, refreshToken, userInfo };
}
