/**
 * 规则管理模块 API
 * 包含规则清单和审查项的相关接口
 */

import { get, post } from '@/api/http';
import { IRequestConfig, IResponseData } from '@/api/types/axios';
import {
  ExampleFeedback,
  ExampleUpdateById,
  PageByVo,
  ReviewRuleUpdateById,
  SaveRequest,
  UpdateByIdRequest,
} from '@/api/types/rule';
/**
 * 请求配置
 * @type {IRequestConfig}
 */
const options: IRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL_PREFIX,
};
/**
 * 新增规则清单
 * @param {SaveRequest} params - 规则清单保存请求参数，包含规则的基本信息
 * @returns {Promise<IResponseData>} 返回保存操作的结果
 */
export function save(params: SaveRequest) {
  return post<IResponseData>('/reviewRuleList/save', params, options);
}

/**
 * 编辑更新规则清单
 * @param {UpdateByIdRequest} params - 规则清单更新请求参数，包含需要更新的规则信息
 * @returns {Promise<IResponseData>} 返回更新操作的结果
 */
export function updateById(params: UpdateByIdRequest) {
  return post<IResponseData>('/reviewRuleList/updateById', params, options);
}

/**
 * 删除规则清单
 * @param {number | string} id - 规则清单ID，用于标识要删除的规则
 * @returns {Promise<IResponseData>} 返回删除操作的结果
 */
export function deleteById(id: number | string) {
  return get<IResponseData>(`/reviewRuleList/deleteById/${id}`, {}, options);
}

/**
 * 查询单个规则清单详情
 * @param {number | string} id - 规则清单ID，用于标识要查询的规则
 * @returns {Promise<IResponseData>} 返回规则清单的详细信息
 */
export function getById(id: number | string) {
  return get<IResponseData>(`/reviewRuleList/getById/${id}`, {}, options);
}

/**
 * 规则清单分页列表
 * @param {PageByVo} params - 分页查询参数，包含页码、每页条数等分页信息
 * @returns {Promise<IResponseData>} 返回分页后的规则清单列表
 */
export function pageByVo(params: PageByVo) {
  return post<IResponseData>(`/reviewRuleList/pageByVo`, params, options);
}

/**
 * 查询单个审查项详情
 * @param {number | string} id - 审查项ID，用于标识要查询的审查项
 * @returns {Promise<IResponseData>} 返回审查项的详细信息
 */
export function reviewRuleGetById(id: number | string) {
  return get<IResponseData>(`/reviewRule/getById/${id}`, {}, options);
}

/**
 * 编辑更新单个审查项
 * @param {ReviewRuleUpdateById} params - 审查项更新请求参数，包含需要更新的审查项信息
 * @returns {Promise<IResponseData>} 返回更新操作的结果
 */
export function reviewRuleUpdateById(params: ReviewRuleUpdateById) {
  return post<IResponseData>(`/reviewRule/updateById`, params, options);
}

/**
 * 保存单个审查项
 * @param {ReviewRuleUpdateById} params - 审查项保存请求参数，包含审查项的基本信息
 * @returns {Promise<IResponseData>} 返回保存操作的结果
 */
export function reviewRuleSave(params: ReviewRuleUpdateById) {
  return post<IResponseData>(`/reviewRule/save`, params, options);
}
export function reviewRuleDeleteById(id: number | string) {
  return get<IResponseData>(`/reviewRule/deleteById/${id}`, {}, options);
}

/**
 * 提交审查示例的反馈信息
 * @param {ExampleFeedback} params - 反馈信息参数，包含审查示例的反馈内容
 * @returns {Promise<IResponseData>} 返回反馈提交的结果
 */
export function reviewRuleExampleFeedback(params: ExampleFeedback) {
  return post<IResponseData>(`/reviewRuleExample/feedback`, params, options);
}

/**
 * 编辑更新审查示例
 * @param {ExampleUpdateById} params - 审查示例更新请求参数，包含需要更新的示例信息
 * @returns {Promise<IResponseData>} 返回更新操作的结果
 */
export function reviewRuleExampleUpdateById(params: ExampleUpdateById) {
  return post<IResponseData>(`/reviewRuleExample/updateById`, params, options);
}

/**
 * 删除审查示例
 * @param {number | string} id - 审查示例ID，用于标识要删除的示例
 * @returns {Promise<IResponseData>} 返回删除操作的结果
 */
export function reviewRuleExampleDeleteById(id: number | string) {
  return get<IResponseData>(`/reviewRuleExample/deleteById/${id}`, {}, options);
}

/**
 * 查询单条审查示例详情
 * @param {number | string} id - 审查示例ID，用于标识要查询的示例
 * @returns {Promise<IResponseData>} 返回审查示例的详细信息
 */
export function reviewRuleExampleGetById(id: number | string) {
  return get<IResponseData>(`/reviewRuleExample/getById/${id}`, {}, options);
}
// 批量导入规则
export function saveBatch(ruleListId: string, params: string[]) {
  return post<IResponseData>(`/reviewRule/saveBatch/${ruleListId}`, JSON.stringify(params), {
    ...options,
    headers: { 'Content-Type': 'application/json' },
  });
}
// 查询可用的规则清单列表
export function listUsefulReviewRuleList(params: any) {
  return post<IResponseData>(`/reviewRuleList/listUsefulReviewRuleList`, params, options);
}
// 开放管理
export function shareConfig(params: any) {
  return post<IResponseData>(`/reviewRuleList/saveShareConfig`, params, options);
}
// 获取开放租户列表
export function listShareTenants(params: any) {
  return post<IResponseData>(`/reviewRuleList/listShareTenants`, params, options);
}
// 规则智能优化
export function intelligentOptimization(params: any) {
  return post('/reviewRule/intelligentOptimization', params, options);
}
