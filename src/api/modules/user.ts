import { get } from '@/api/http';
import { IRequestConfig } from '@/api/types/axios';

const options: IRequestConfig = {
  baseURL: `${import.meta.env.VITE_API_URL}/user`,
};

interface Menu {
  // 定义菜单接口的字段
  id: string;
  name: string;
  path: string;
  icon?: string;
  children?: Menu[];
}

/**
 * 根据用户GUID获取菜单
 * @param guid 用户唯一标识
 * @returns {Promise<Menu[]>} 返回菜单列表
 */
export function getMenuByGuid(guid: string) {
  return get<Menu[]>('/role/getMenuByGuid', { guid, productCode: import.meta.env.VITE_APP_CODE }, options);
}

/**
 * 根据用户GUID获取用户信息
 * @param userGuid 用户唯一标识
 * @returns {Promise<any>} 返回用户信息
 */
export function getUserByGuid(userGuid: string) {
  return get('/user/getByGuid', { userGuid }, options);
}
