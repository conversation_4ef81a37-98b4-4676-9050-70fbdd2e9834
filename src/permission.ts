import 'nprogress/nprogress.css'; // 进度条样式

import NProgress from 'nprogress'; // 进度条
import { MessagePlugin } from 'tdesign-vue-next';
import { RouteRecordRaw } from 'vue-router';

import router from '@/router';
import { getPermissionStore, useAppStore, useUserStore } from '@/store';
import { PAGE_NOT_FOUND_ROUTE } from '@/utils/route/constant';

import { getQueryString } from './utils';
import { doSSOLogin, jumpLogin } from './utils/auth';

// 配置NProgress
NProgress.configure({ showSpinner: false });

// 路由前置守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start();

  const permissionStore = getPermissionStore();
  const { whiteListRouters } = permissionStore;
  const appStore = useAppStore();
  const userStore = useUserStore();

  // 缓存非白名单页面地址
  if (!whiteListRouters.includes(to.path) && to.path !== '/') {
    appStore.setFullPath(to.fullPath);
    appStore.setPreFullPath(from.fullPath);
  }

  const state = (to.query.state || getQueryString('state') || import.meta.env.VITE_APP_STATE) as string;
  const code = (to.query.code || getQueryString('code')) as string;

  // 处理未登录情况
  if (!userStore.token) {
    if (whiteListRouters.includes(to.path)) {
      next();
    } else if (state && code) {
      try {
        await doSSOLogin(state, code);
        next();
      } catch (e) {
        next('/500');
      }
    } else {
      jumpLogin({ state });
      next(false);
    }
    NProgress.done();
    return;
  }

  // 处理已登录情况
  if (to.path === '/login') {
    next();
    NProgress.done();
    return;
  }

  try {
    const { asyncRoutes } = permissionStore;
    if (!asyncRoutes?.length) {
      const routeList = await permissionStore.buildAsyncRoutes();
      routeList.forEach((item: RouteRecordRaw) => router.addRoute(item));
      // console.log('异步路由列表1：', routeList);
      // console.log('异步路由列表2：', router);
      // console.log('异步路由列表3：', router.getRoutes());
      // console.log('异步路由列表4：', to);
      // console.log('异步路由列表5：', from);
      if (to.name === PAGE_NOT_FOUND_ROUTE.name) {
        next({ path: to.fullPath, replace: true, query: to.query });
      } else {
        const redirect = decodeURIComponent((from.query.redirect || to.path) as string);
        next(to.path === redirect ? { ...to, replace: true } : { path: redirect, query: to.query });
      }
    } else if (router.hasRoute(to.name)) {
      next();
    } else {
      next('/');
    }
  } catch (error) {
    // MessagePlugin.error(error.message);
    try {
      if (state && code) {
        await doSSOLogin(state, code);
        next();
      } else {
        await jumpLogin({ state });
        next(false);
      }
    } catch (e) {
      MessagePlugin.error(e.message || '登录跳转失败');
      next(false);
    }
  } finally {
    NProgress.done();
  }
});

// 路由后置守卫
router.afterEach((to) => {
  // 登录页清理用户状态
  if (to.path === '/login') {
    const userStore = useUserStore();
    const permissionStore = getPermissionStore();
    userStore.logout();
    permissionStore.restoreRoutes();
  }
  NProgress.done();
});
