/* eslint-disable simple-import-sort/imports */
import TDesign from 'tdesign-vue-next';
import { createApp } from 'vue';

import App from './App.vue';
import router from './router';
import { store } from './store';
import i18n from './locales';
import { permission } from './directives';

import 'tdesign-vue-next/es/style/index.css';
import '@/style/index.less';
import '@/themes/theme.css';
import './permission';
import util from '@/utils';

const app = createApp(App);

app.use(TDesign);
app.use(store);
app.use(router);
app.use(i18n);
app.use(util);
app.directive('permission', permission);

app.mount('#app');
