import type { Directive } from 'vue';
// 权限指令
const permission: Directive = {
  mounted: (el: HTMLElement, binding: any) => {
    const { value } = binding;
    const CHECK_PERMISSION = import.meta.env.VITE_MENU_ENABLE?.toBoolean();
    if (CHECK_PERMISSION && !value && el.parentNode) {
      el.parentNode?.removeChild(el);
    }
  },
  updated: (el: HTMLElement, binding: any) => {
    const { value } = binding;
    const CHECK_PERMISSION = import.meta.env.VITE_MENU_ENABLE?.toBoolean();
    if (CHECK_PERMISSION && !value && el.parentNode) {
      el.parentNode?.removeChild(el);
    }
  },
};
export { permission };
