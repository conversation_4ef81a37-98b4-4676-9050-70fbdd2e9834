import { defineStore } from 'pinia';
import { RouteRecordRaw } from 'vue-router';

import { getMenuByGuid } from '@/api/modules/user';
import router, { fixedRouterList } from '@/router';
import { store, useUserStore } from '@/store';
import { filterRouter } from '@/utils/route';
import { PAGE_NOT_FOUND_ROUTE } from '@/utils/route/constant';
// 定义默认重定向路径为常量
const DEFAULT_LOGIN_REDIRECT_PATH = '/login';

// 假设 Router 类型如下（根据实际情况调整）
interface Route {
  path: string;
  name?: string;
  hidden?: boolean;
  redirect?: string;
  children?: Route[];
}

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    whiteListRouters: ['/login', '/result', '/403', '/404', '/500'],
    routers: [],
    removeRoutes: [],
    asyncRoutes: [],
  }),
  actions: {
    async initRoutes() {
      const accessedRouters = this.asyncRoutes;

      // 在菜单展示全部路由
      // this.routers = [...accessedRouters, ...fixedRouterList];
      // 在菜单只展示动态路由和首页
      // this.routers = [...homepageRouterList, ...accessedRouters];
      // 在菜单只展示动态路由
      this.routers = [...accessedRouters];
    },
    async buildAsyncRoutes() {
      try {
        // 发起菜单权限请求 获取菜单列表
        const { userInfo, getSuperUser } = useUserStore();
        await getSuperUser(userInfo.guid);
        const permissionList: any = await getMenuByGuid(userInfo.guid);
        const routers = await filterRouter(permissionList, [...fixedRouterList]);
        const routesList: Route[] = [PAGE_NOT_FOUND_ROUTE, ...routers];

        // 取第一个为首页 如果没有首页 则取登录页
        if (routers.length) {
          const firstRouter = routers[0];
          let redirect = firstRouter?.path ?? DEFAULT_LOGIN_REDIRECT_PATH;

          if (firstRouter?.children?.length) {
            const firstChild = firstRouter.children[0];
            redirect = firstChild?.path ?? redirect;
          }

          const HOME_ROUTE: Route = {
            path: '/',
            name: 'HomeRedirect',
            hidden: true,
            redirect,
          };

          routesList.push(HOME_ROUTE);
        } else {
          const HOME_ROUTE: Route = {
            path: '/',
            name: 'HomeRedirect',
            hidden: true,
            redirect: DEFAULT_LOGIN_REDIRECT_PATH,
          };

          routesList.push(HOME_ROUTE);
        }

        this.asyncRoutes = routesList;

        await this.initRoutes();
        return this.asyncRoutes;
      } catch (error) {
        console.log(error);
        // throw new Error("Can't build routes");
      }
    },
    async restoreRoutes() {
      // 不需要在此额外调用initRoutes更新侧边导肮内容，在登录后asyncRoutes为空会调用
      this.asyncRoutes.forEach((item: RouteRecordRaw) => {
        if (item.name) {
          router.removeRoute(item.name);
        }
      });
      this.asyncRoutes = [];
    },
  },
});

export function getPermissionStore() {
  return usePermissionStore(store);
}
