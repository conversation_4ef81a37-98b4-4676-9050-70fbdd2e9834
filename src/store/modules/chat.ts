import { defineStore } from 'pinia';

import { MessageProps } from '@/api/types/task';

interface ChatState {
  hidePromote: boolean;
  messages: MessageProps[];
  isStreaming: boolean;
  stopStream: () => void;
}

export const useChatStore = defineStore('chatState', {
  state: (): ChatState => {
    return {
      hidePromote: false,
      messages: [],
      isStreaming: false,
      stopStream: () => {},
    };
  },
  // 开启数据持久化
  persist: {
    storage: localStorage,
    paths: ['messages', 'hidePromote'],
  },
  actions: {
    updateMessages(messages: MessageProps[]) {
      this.messages = messages;
    },
    updateStreaming(bool: boolean) {
      this.isStreaming = bool;
    },
    updateStopStream(fn: () => void) {
      this.stopStream = fn;
    },
    updatePromote(bool: boolean) {
      this.hidePromote = bool;
    },
  },
});
