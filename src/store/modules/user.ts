import { defineStore } from 'pinia';

import { getSSOConfig, getUserInfo, login, ssoLogin } from '@/api/modules/auth';
import { getUserByGuid } from '@/api/modules/user';
import { AuthInfo } from '@/api/types/user';
import { store, usePermissionStore } from '@/store';

// 认证状态接口
interface AuthState {
  token: string; // 认证token
  refreshToken: string; // 刷新 token
  userInfo: AuthInfo | null; // 用户信息
  redirectUrl: string; // SSO重定向URL
  appCode: string; // SSO应用凭证
}

// 认证状态管理
export const useUserStore = defineStore('user', {
  state: (): AuthState => ({
    token: '',
    refreshToken: '',
    userInfo: null,
    redirectUrl: '',
    appCode: import.meta.env.VITE_APP_CODE,
  }),

  getters: {
    // 获取token
    getToken: (state) => state.token,
    // 获取refreshToken
    getRefreshToken: (state) => state.refreshToken,
    // 获取用户信息
    getUserInfo: (state) => state.userInfo,
    // 获取重定向URL
    getRedirectUrl: (state) => state.redirectUrl,
  },

  // 数据持久化配置
  persist: {
    afterRestore: () => {
      const permissionStore = usePermissionStore();
      permissionStore.initRoutes();
    },
    storage: localStorage,
  },

  actions: {
    // 设置token
    setToken(token: string) {
      this.token = token;
    },
    // 设置refreshToken
    setRefreshToken(refreshToken: string) {
      this.refreshToken = refreshToken;
    },
    // 设置用户信息
    setUserInfo(userInfo: AuthInfo) {
      this.userInfo = { ...userInfo };
    },
    // 设置重定向URL
    setRedirectUrl(url: string) {
      this.redirectUrl = url;
    },
    // 清除认证信息
    clearAuth() {
      this.token = '';
      this.userInfo = null;
      this.redirectUrl = '';
    },
    // 获取SSO配置
    async getAuthUrl(appCode: string, state: string): Promise<string> {
      try {
        // 获取SSO配置并解构重定向URL
        const { redirectFullUrl = '' } = await getSSOConfig(appCode, state);

        // 校验重定向URL是否有效
        if (!redirectFullUrl?.trim()) {
          throw new Error('SSO 重定向 URL 为空');
        }

        // 更新状态并返回重定向URL
        this.setRedirectUrl(redirectFullUrl);
        return redirectFullUrl;
      } catch (error) {
        // 捕获并重新抛出错误，便于上层处理
        throw new Error(`获取SSO配置失败: ${error instanceof Error ? error.message : error}`);
      }
    },
    /**
     * 执行登录
     * @param params 登录参数
     * @returns Promise<void>
     */
    async login(params: any): Promise<boolean> {
      const { access_token: token } = await login(params);

      this.setToken(token);
      const userInfo = await getUserInfo();
      this.setUserInfo(userInfo);
      return !!token;
    },
    /**
     * 执行SSO登录
     * @param state 状态参数
     * @param code 授权码
     * @returns Promise<void>
     */
    async doLoginForSSO(state: string, code: string): Promise<void> {
      const { appCode } = useUserStore();
      const { token, refreshToken, userInfo } = await ssoLogin(appCode, state, code);
      this.setToken(token);
      this.setRefreshToken(refreshToken);
      this.setUserInfo(userInfo);
    },
    /**
     * 登出
     * @returns Promise<void>
     */
    async logout(): Promise<void> {
      this.clearAuth();
    },
    async getSuperUser(guid: string): Promise<void> {
      const userInfo = await getUserByGuid(guid);
      this.setUserInfo(userInfo);
    },
  },
});

// 在setup之外使用store的快捷方法
export function useUserStoreWithOut() {
  return useUserStore(store);
}
