import { defineStore } from 'pinia';

import { store } from '@/store';

// 应用状态接口
interface AppState {
  fullPath: string; // 当前完整路径
  preFullPath: string; // 前一个完整路径
  routeHistory: string[]; // 路由历史记录
}

// 应用状态管理
export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    fullPath: '',
    preFullPath: '',
    routeHistory: [],
  }),

  getters: {
    // 获取路由历史记录
    getRouteHistory: (state) => state.routeHistory,
  },

  // 数据持久化配置
  persist: {
    storage: localStorage,
  },

  actions: {
    // 设置当前完整路径
    setFullPath(to: string) {
      this.fullPath = to;
      // 更新路由历史记录，最多保留10条
      this.routeHistory = [to, ...this.routeHistory.slice(0, 9)];
    },
    // 设置前一个完整路径
    setPreFullPath(from: string) {
      this.preFullPath = from;
    },
  },
});

// 在setup之外使用store的快捷方法
export function useAppStoreWithOut() {
  return useAppStore(store);
}
