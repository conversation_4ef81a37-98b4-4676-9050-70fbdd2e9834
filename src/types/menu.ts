import { DefineComponent } from 'vue';
import { RouteRecord } from 'vue-router';

export type RouteRecordEx = RouteRecord & {
  url: string;
  id?: string;
  parentId?: string;
  orderNum?: number;
  menuName?: string;
  icon?: string;
  sort?: number;
  hidden?: boolean;
  status?: boolean;
  menuBlockVos?: {
    blockKey: string;
    blockName: string;
  }[];
  meta?: {
    title: string;
    icon: string;
    actions: Record<string, string>;
  };
  component?: DefineComponent;
  children?: RouteRecordEx[];
  redirect?: string;
  name?: string;
  path?: string;
  [key: string]: any;
};
