/**
 * 工具方法类型声明文件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/9/19 16:32:06
 * LastEditors: mi<PERSON>zhen<PERSON> <EMAIL>
 * LastEditTime: 2022-09-26 10:26:38
 */

/**
 * 数组转树形配置项接口
 */
export interface IArrayToTree {
  idKey?: string; // 主键字段名
  pidKey?: string; // 父级字段名
  children?: string; // 子节点字段名
}

export interface IDictOptions {
  fullPath?:boolean;
  type?:string
}
export interface IDictResult {
  label: string | number | boolean;
  value: string | number;
  children?: Array<any>;
}