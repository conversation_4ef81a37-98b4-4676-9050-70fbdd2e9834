export interface ExampleList {
  /**
   * 合同内容
   */
  contractContent: string;
  /**
   * 正确结果：NONE-无风险、RISK-存在风险
   */
  correctResult: string;
  id?: string;
  /**
   * 审查说明
   */
  reviewDescription: string;
  /**
   * 关联的审查规则ID
   */
  reviewRuleId?: string;
  /**
   * 规则来源：FEEDBACK-用户反馈，BUILTIN-内置
   */
  ruleOrigin: string;
  /**
   * 租住ID
   */
  tenantId?: string;
  /**
   * 用户ID
   */
  userId?: string;
  [property: string]: any;
}
/**
 * 审查项（规则项）数据结构
 */
export interface RuleVo {
  /**
   * 标准编码(后台生成)
   */
  code?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 规则名称
   */
  name: string;
  /**
   * 关联关系ID
   */
  relationId?: string;
  /**
   * 所属清单ID
   */
  ruleListId: string;
  /**
   * 风险等级：LOW-低风险、MEDIUM-中风险、HIGH-高风险
   */
  riskLevel?: string;
  /**
   * 审查规则
   */
  rule: string;
  /**
   * 审查示例
   */
  exampleList?: ExampleList[];
  /**
   * 规则来源：LLM、MANUAL
   */
  ruleSource?: string;
  /**
   * 租户id
   */
  tenantId?: string;
  /**
   * 用户id
   */
  userId?: string;
  [property: string]: any;
}

/**
 * 审查清单数据结构
 */
export interface RuleVoDetail {
  /**
   * 审查清单描述
   */
  description?: string;
  /**
   * id，ID 编号
   */
  id: string;
  /**
   * 审查清单名称
   */
  name: string;
  /**
   * 审查项
   */
  ruleVos?: RuleVo[];
  /**
   * 共享范围：GENERAL、TENANT_SHARED、PRIVATE
   */
  shareScope?: string;
  /**
   * 来源类型：SYSTEM-系统内置，CUSTOM-客户自定义
   */
  sourceType?: string;
  /**
   * 租户id
   */
  tenantId?: string;
  [property: string]: any;
}

/**
 * 智能优化结果结构
 */
export interface OptimizationResult {
  /**
   * 优化建议
   */
  suggestion: string;
  /**
   * 优化后的规则内容
   */
  result: string;
}
