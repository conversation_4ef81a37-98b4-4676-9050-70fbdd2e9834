// 通用声明

// Vue
declare module '*.vue' {
  import { DefineComponent } from 'vue';

  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare type ClassName = { [className: string]: any } | ClassName[] | string;

declare module '*.svg' {
  const CONTENT: string;
  export default CONTENT;
}

declare type Recordable<T = any> = Record<string, T>;

interface Window {
  randomString: () => string;
}

interface String {
  toBoolean: (this: string) => boolean;
}

interface Array {
  unique: <T>(isSort: boolean) => T[];
  uniqueForObject: <T>(field?: string) => T[];
}

interface Date {
  format: (format: string) => string;
}

declare module '*.md' {
  // "unknown" would be more detailed depends on how you structure frontmatter
  const attributes: Record<string, unknown>;

  // When "Mode.TOC" is requested
  const toc: { level: string; content: string }[];

  // When "Mode.HTML" is requested
  const html: string;

  // When "Mode.RAW" is requested
  const raw: string;

  // When "Mode.React" is requested. VFC could take a generic like React.VFC<{ MyComponent: TypeOfMyComponent }>
  import React from 'react';

  const ReactComponent: React.VFC;

  // When "Mode.Vue" is requested
  import { Component, ComponentOptions } from 'vue';

  const VueComponent: ComponentOptions;
  const VueComponentWith: (components: Record<string, Component>) => ComponentOptions;

  // Modify below per your usage
  export { attributes, html, ReactComponent, toc, VueComponent, VueComponentWith };
}
