/*
 * 系统管理路由
 * @Author: miaozhen<PERSON> <EMAIL>
 * @Date: 2024-05-10 16:10:39
 * @LastEditors: miaozhendong <EMAIL>
 * @LastEditTime: 2024-05-13 14:19:21
 */
import { LAYOUT } from '@/utils/route/constant';

export default [
  {
    path: '/contract-review',
    name: 'ContractReview',
    component: LAYOUT,
    redirect: '/contract-review/task-management',
    sort: 6,
    meta: {
      title: '合同审查',
      icon: 'book-open',
    },
    children: [
      {
        path: '/contract-review/task-management',
        name: 'TaskManagement',
        meta: { title: '审查任务管理' },
        component: () => import('@/pages/contract-review/task-management/index.vue'),
      },
      {
        path: '/contract-review/create-task',
        name: 'CreateTaskManagement',
        hidden: true,
        meta: { title: '新建任务', noCache: true },
        component: () => import('@/pages/contract-review/task-management/createTask.vue'),
      },
      {
        path: '/contract-review/rule-configuration',
        name: 'RuleConfiguration',
        meta: { title: '审查规则配置' },
        component: () => import('@/pages/contract-review/rule-configuration/index.vue'),
      },
      {
        path: '/contract-review/create-rule/:id',
        name: 'CreateRuleConfiguration',
        hidden: true,
        meta: { title: '规则详情', noCache: true },
        component: () => import('@/pages/contract-review/rule-configuration/review/index.vue'),
      },
      {
        path: '/contract-review/open-management/:id',
        name: 'OpenManagement',
        hidden: true,
        meta: { title: '开放管理', noCache: true },
        component: () => import('@/pages/contract-review/rule-configuration/open-management/index.vue'),
      },
      {
        path: '/contract-review/api-configuration',
        name: 'ApiConfiguration',
        meta: { title: 'API配置管理' },
        component: () => import('@/pages/contract-review/api-configuration/index.vue'),
      },
    ],
  },
];
