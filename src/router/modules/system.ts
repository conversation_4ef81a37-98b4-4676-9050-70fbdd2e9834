/*
 * 系统管理路由
 * @Author: mi<PERSON>zhen<PERSON> <EMAIL>
 * @Date: 2024-05-10 16:10:39
 * @LastEditors: miaozhen<PERSON> <EMAIL>
 * @LastEditTime: 2024-05-13 14:19:21
 */
import { LAYOUT } from '@/utils/route/constant';

export default [
  {
    path: '/system',
    name: 'System',
    component: LAYOUT,
    redirect: '/system/model',
    sort: 6,
    meta: {
      title: '平台配置',
      icon: 'setting',
    },
    children: [
      {
        path: '/system/model',
        name: 'Model',
        meta: { title: '模型配置' },
        component: () => import('@/pages/system/model/index.vue'),
      },
      {
        path: '/system/model-create',
        name: 'ModelCreate',
        hidden: true,
        meta: { title: '配置管理' },
        component: () => import('@/pages/system/model/create.vue'),
      },
      {
        path: '/system/tenant',
        name: 'Tenant',
        meta: { title: '租户配置' },
        component: () => import('@/pages/system/tenant/index.vue'),
      },
    ],
  },
];
