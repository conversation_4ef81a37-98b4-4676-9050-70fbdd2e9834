import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosRequestHeaders,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import isFunction from 'lodash/isFunction';
import throttle from 'lodash/throttle';
import { stringify } from 'qs';

import { ContentTypeEnum } from '@/constants';
import { AxiosRequestConfigRetry, RequestOptions, Result } from '@/types/axios';

import { AxiosCanceler } from './AxiosCancel';
import { CreateAxiosOptions } from './AxiosTransform';

/**
 * Axios 模块
 */
export class VAxios {
  /**
   * Axios实例句柄
   * @private
   */
  private instance: AxiosInstance;

  /**
   * Axios配置
   * @private
   */
  private readonly options: CreateAxiosOptions;

  private readonly axiosCanceler: AxiosCanceler;

  constructor(options: CreateAxiosOptions) {
    this.options = options;
    this.axiosCanceler = new AxiosCanceler();
    this.instance = axios.create(options);
    this.setupInterceptors();
  }

  /**
   * 创建Axios实例
   * @param config
   * @private
   */
  private createAxios(config: CreateAxiosOptions): void {
    this.instance = axios.create(config);
  }

  /**
   * 获取数据处理类
   * @private
   */
  private getTransform() {
    return this.options.transform;
  }

  /**
   * 获取Axios实例
   */
  getAxios(): AxiosInstance {
    return this.instance;
  }

  /**
   * 配置Axios
   * @param config
   */
  configAxios(config: CreateAxiosOptions) {
    if (!this.instance) return;
    this.createAxios(config);
  }

  /**
   * 设置公共头部信息
   * @param headers
   */
  setHeader(headers: Record<string, string>): void {
    if (!this.instance) return;
    Object.assign(this.instance.defaults.headers, headers);
  }

  /**
   * 设置拦截器
   * @private
   */
  private setupInterceptors(): void {
    const transform = this.getTransform();
    if (!transform) return;

    const { requestInterceptors, requestInterceptorsCatch, responseInterceptors, responseInterceptorsCatch } =
      transform;

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        const { ignoreCancelToken } = (config as any).requestOptions || {};
        const ignoreCancel = ignoreCancelToken ?? this.options.requestOptions?.ignoreCancelToken;

        if (!ignoreCancel) {
          this.axiosCanceler.addPending(config);
        }

        if (requestInterceptors && isFunction(requestInterceptors)) {
          config = requestInterceptors(config, this.options) as InternalAxiosRequestConfig;
        }

        return config;
      },
      (error: AxiosError) => {
        if (requestInterceptorsCatch && isFunction(requestInterceptorsCatch)) {
          return requestInterceptorsCatch(error);
        }
        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (res: AxiosResponse) => {
        if (res) {
          this.axiosCanceler.removePending(res.config);
        }
        if (responseInterceptors && isFunction(responseInterceptors)) {
          res = responseInterceptors(res);
        }
        return res;
      },
      (error: AxiosError) => {
        if (responseInterceptorsCatch && isFunction(responseInterceptorsCatch)) {
          return responseInterceptorsCatch(error, this.instance);
        }
        return Promise.reject(error);
      },
    );
  }

  /**
   * 支持 FormData 请求格式
   * @param config
   */
  private supportFormData(config: AxiosRequestConfig): AxiosRequestConfig {
    const headers = config.headers || (this.options.headers as AxiosRequestHeaders);
    const contentType = headers?.['Content-Type'] || headers?.['content-type'];

    if (
      contentType !== ContentTypeEnum.FormURLEncoded ||
      !Reflect.has(config, 'data') ||
      config.method?.toUpperCase() === 'GET'
    ) {
      return config;
    }

    return {
      ...config,
      data: stringify(config.data, { arrayFormat: 'brackets' }),
    };
  }

  /**
   * 支持 params 序列化
   * @param config
   */
  private supportParamsStringify(config: AxiosRequestConfig): AxiosRequestConfig {
    const headers = config.headers || this.options.headers;
    const contentType = headers?.['Content-Type'] || headers?.['content-type'];

    if (contentType === ContentTypeEnum.FormURLEncoded || !Reflect.has(config, 'params')) {
      return config;
    }

    return {
      ...config,
      paramsSerializer: (params: any) => stringify(params, { arrayFormat: 'brackets' }),
    };
  }

  get<T = any>(config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
    return this.request({ ...config, method: 'GET' }, options);
  }

  post<T = any>(config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
    return this.request({ ...config, method: 'POST' }, options);
  }

  put<T = any>(config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
    return this.request({ ...config, method: 'PUT' }, options);
  }

  delete<T = any>(config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
    return this.request({ ...config, method: 'DELETE' }, options);
  }

  patch<T = any>(config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
    return this.request({ ...config, method: 'PATCH' }, options);
  }

  /**
   * 上传文件封装
   * @param key 文件所属的key
   * @param file 文件
   * @param config 请求配置
   * @param options
   */
  upload<T = any>(key: string, file: File, config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
    const params: FormData = config.params ?? new FormData();
    params.append(key, file);

    return this.request(
      {
        ...config,
        method: 'POST',
        headers: {
          'Content-Type': ContentTypeEnum.FormData,
        },
        params,
      },
      options,
    );
  }

  /**
   * 请求封装
   * @param config
   * @param options
   */
  request<T = any>(config: AxiosRequestConfigRetry, options?: RequestOptions): Promise<T> {
    const { requestOptions } = this.options;

    if (requestOptions.throttle !== undefined && requestOptions.debounce !== undefined) {
      throw new Error('throttle and debounce cannot be set at the same time');
    }

    if (requestOptions.throttle?.delay) {
      return new Promise((resolve) => {
        throttle(() => resolve(this.synthesisRequest(config, options)), requestOptions.throttle.delay);
      });
    }

    if (requestOptions.debounce?.delay) {
      return new Promise((resolve) => {
        debounce(() => resolve(this.synthesisRequest(config, options)), requestOptions.debounce.delay);
      });
    }

    return this.synthesisRequest(config, options);
  }

  /**
   * 请求方法
   * @private
   */
  private async synthesisRequest<T = any>(config: AxiosRequestConfigRetry, options?: RequestOptions): Promise<T> {
    let conf: CreateAxiosOptions = cloneDeep(config);
    const transform = this.getTransform();
    const { requestOptions } = this.options;
    const opt: RequestOptions = { ...requestOptions, ...options };

    const { beforeRequestHook, requestCatchHook, transformRequestHook } = transform || {};

    if (beforeRequestHook && isFunction(beforeRequestHook)) {
      conf = beforeRequestHook(conf, opt);
    }
    conf.requestOptions = opt;

    conf = this.supportFormData(conf);

    try {
      const res = await this.instance.request<any, AxiosResponse<Result>>(!config.retryCount ? conf : config);

      if (transformRequestHook && isFunction(transformRequestHook)) {
        return transformRequestHook(res, opt);
      }

      return res as unknown as T;
    } catch (error) {
      if (requestCatchHook && isFunction(requestCatchHook)) {
        return Promise.reject(requestCatchHook(error, opt));
      }

      if (axios.isAxiosError(error)) {
        // 处理 Axios 错误
        const axiosError = error as AxiosError;
        return Promise.reject(new Error(axiosError.message || '请求失败'));
      }

      return Promise.reject(error);
    }
  }
}
