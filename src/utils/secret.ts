/**
 * Created by <PERSON><PERSON><PERSON><PERSON>@live.com on 2021/9/26.
 */
import CryptoJS from 'crypto-js'
// AES密钥
export const AES_KEY = 'NmQ4YzkOKTgzYzA='
// AES密钥偏移量
export const AES_IV = 'NmQ4YzkOKTg1234='

/**
 * AES加密
 * @param {String} word 加密内容
 * @param {String} key 密钥
 * @param {String}  iv 密钥偏移量
 * @param {String} encodeFormat 编码格式【base64：base64编码，默认hex: 16进制编码】
 * @returns {string}
 */
export const cryptoEncrypt = (word:string, { key = AES_KEY, iv = AES_IV, encodeFormat = 'hex' } = {}) => {
  // 十六位十六进制数作为密钥
  const _key = CryptoJS.enc.Utf8.parse(key)
  // 十六位十六进制数作为密钥偏移量
  const _iv = CryptoJS.enc.Utf8.parse(iv)
  let msg:string = ''
  if (typeof word === 'string') {
    msg = word
  } else if (typeof word === 'object') {
    // 对象格式的转成json字符串
    msg = CryptoJS.enc.Utf8.parse(word)
  }
  const encrypted = CryptoJS.AES.encrypt(msg, _key, {
    iv: _iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  let str = encrypted.ciphertext.toString()
  // base64编码转换
  if(encodeFormat === 'base64') {
    str = CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
  }
  return str
}

/**
 * AES解密
 * @param {String} word 解密内容
 * @param {String} key 密钥
 * @param {String} iv 密钥偏移量
 * @param {String} encodeFormat 编码格式【默认base64：base64编码，hex: 16进制编码】
 * @return {string}
 */
export const cryptoDecrypt = (word:string, { key = AES_KEY, iv = AES_IV, encodeFormat = 'Base64' } = {}) => {
  const _key = CryptoJS.enc.Utf8.parse(key)
  const _iv = CryptoJS.enc.Utf8.parse(iv)
  let msg = word
  // Base64编码转换
  if(encodeFormat === 'Base64') {
    const encryptedHexStr = CryptoJS.enc.Base64.parse(word)
    msg = CryptoJS.enc.Base64.stringify(encryptedHexStr)
  }
  const decrypt = CryptoJS.AES.decrypt(msg, _key, {
    iv: _iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  return decryptedStr.toString()
}
