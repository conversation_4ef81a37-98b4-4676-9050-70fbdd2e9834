// 获取常用时间
import dayjs from 'dayjs';
import { computed } from 'vue';

export const LAST_7_DAYS = [
  dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
];

export const LAST_30_DAYS = [
  dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
  dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
];

/**
 * 计算从指定时间到现在的时间差
 * @param createdTime - 创建时间，格式为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间差字符串，例如 "1小时30分钟20秒"
 */
export function getTimeDiff(createdTime: string): string {
  if (!createdTime) return '0秒';

  try {
    const createDate = new Date(createdTime);
    const now = new Date();
    const diff = Math.floor((now.getTime() - createDate.getTime()) / 1000); // 转为秒

    if (diff <= 0) return '0秒';

    const hours = Math.floor(diff / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    const seconds = diff % 60;

    let result = '';
    if (hours > 0) {
      result += `${hours}小时`;
    }
    if (minutes > 0) {
      result += `${minutes}分钟`;
    }
    if (seconds > 0) {
      result += `${seconds}秒`;
    }

    return result || '0秒';
  } catch (error) {
    console.error('计算时间差错误:', error);
    return '计算错误';
  }
}

/**
 * 创建一个响应式的时间差显示hooks
 * @param createdTime - 创建时间的ref
 * @returns 格式化的时间差字符串的ref
 */
export function useTimeDiff(createdTime: string) {
  // 每次调用都重新计算，避免依赖定时器
  return computed(() => getTimeDiff(createdTime));
}
