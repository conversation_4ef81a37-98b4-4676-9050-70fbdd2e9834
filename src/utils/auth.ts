import Router from '@/router';
import { useAppStore, useUserStore } from '@/store';

interface ConfigState {
  appCode?: string; // 应用凭证
  state?: string; // 客户端凭证
  redirect?: boolean; // 重定向标识，false: 进入首页
  delay?: number; // 延迟时间（毫秒）
  sso?: boolean; // 是否开启SSO
  code?: string; // sso 票据
}

/**
 * 执行延迟操作
 * @param callback 回调函数
 * @param delay 延迟时间
 */
const executeWithDelay = async (callback: () => void, delay?: number): Promise<void> => {
  if (delay && delay > 0) {
    await new Promise<void>((resolve) => {
      setTimeout(resolve, delay);
    });
  }
  callback();
};

/**
 * 登录
 * @param params 页面配置参数
 * @returns {Promise<void>}
 * @throws 当SSO模式下缺少必要参数时抛出错误
 */
export async function jumpLogin(params: ConfigState = {}): Promise<void> {
  // 解构参数并设置默认值
  const {
    appCode = import.meta.env.VITE_APP_CODE,
    state = import.meta.env.VITE_APP_STATE,
    redirect = true,
    delay = 0,
    sso = import.meta.env.VITE_SSO_ENABLE.toBoolean(),
  } = params;

  // 清理用户Token缓存
  const userStore = useUserStore();
  await userStore.logout();

  // 处理延迟跳转
  const handleRedirect = async (callback: () => void) => {
    await executeWithDelay(callback, delay);
  };

  // SSO模式处理
  if (sso) {
    // 获取重定向URL
    const userStoreForSSO = useUserStore();
    const url = userStoreForSSO.redirectUrl || (await userStoreForSSO.getAuthUrl(appCode, state));

    // 构建目标路径
    const currentUrl = `${window.location.origin}${window.location.pathname}`;
    const targetPath = redirect ? currentUrl : '/';

    // 处理并编码最终URL
    const finalUrl = decodeURIComponent(url).replace('$redirectUri', targetPath);

    // 执行重定向
    await handleRedirect(() => {
      window.location.href = finalUrl;
    });
  } else {
    // 非SSO模式，跳转至登录页
    await handleRedirect(() => {
      Router.replace('/login');
    });
  }
}

/**
 * 执行SSO登录
 * @param state 状态参数，用于SSO认证
 * @param code 授权码，用于SSO认证
 * @returns {Promise<void>}
 */
export async function doSSOLogin(state: string, code: string) {
  const appStore = useAppStore();
  const userStore = useUserStore();
  // 使用SSO方式进行用户登录
  await userStore.doLoginForSSO(state, code);

  // 获取完整路径，如果不存在则使用空字符串
  const path = appStore.fullPath || '';

  // 清除SSO重定向URL，防止重复使用
  userStore.setRedirectUrl('');

  // 使用href跳转页面，防止code缓存导致异常
  window.location.href = `${window.location.origin + window.location.pathname}#${path}`;
}
