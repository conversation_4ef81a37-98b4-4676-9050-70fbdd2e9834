import type { IArrayToTree } from '@/types/util';

/**
 * URL参数提取
 * @param name 参数名
 * @returns {string | null} 如果参数不存在返回 null，否则返回解码后的参数值
 */
export function getQueryString(name: string): string | null {
  const searchParams = new URLSearchParams(window.location.search);
  const value = searchParams.get(name);
  return value ? decodeURIComponent(value) : null;
}

/**
 * 一维数组转换树型结构
 * @param {Array} arr 一维数组
 * @param options 配置项
 * @returns {Array<T>} 多维数组
 */
export function arrayToTree<T>(arr: Array<T>, options: IArrayToTree = {}) {
  const { idKey = 'id', pidKey = 'parentId', children = 'children' } = options;
  const result: Array<T> = [];
  // 存Map映射，方便取值
  const map: Record<string, any> = arr.reduce((res: Record<string, any>, item: Record<string, any>) => {
    res[item[idKey]] = item;
    return res;
  }, {});
  arr.forEach((item: Record<string, any>) => {
    // 父节点
    const parent = map[item[pidKey]];
    if (parent) {
      (parent[children] || (parent[children] = [])).push(item);
    } else {
      result.push(item as T);
    }
  });
  return result;
}

/**
 * 树型结构转换一维数组
 * @param {Array} tree 多维数组
 * @param options 配置项
 * @return {Array<T>} 一维数组
 */
export function treeToArray<T>(tree: Array<T>, options: IArrayToTree = {}): Array<T> {
  const { children = 'children' } = options;
  return tree.reduce((arr: Record<string, any>, obj: Record<string, any>) => {
    const childList = obj[children] || [];
    delete obj[children];
    return arr.concat([obj], treeToArray(childList)) as Array<T>;
  }, []) as Array<T>;
}

export default {
  // 初始化方法
  install() {
    /**
     * 将字符串转换为Boolean对象
     * @returns {boolean}
     */
    window.String.prototype.toBoolean = function () {
      return /^true$/i.test(this);
    };

    /**
     * 格式化日期
     * @param format 格式化格式
     *  eg: Format('yyyy-MM-dd hh:mm:ss ww qq')
     * @returns {string}
     */
    window.Date.prototype.format = function (format = 'yyyy-MM-dd hh:mm:ss') {
      let fmt = format;

      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, `${this.getFullYear()}`.substr(4 - RegExp.$1.length));
      }

      if (/(w+)/.test(fmt)) {
        // 周
        const map = new Map([
          ['0', '日'],
          ['1', '一'],
          ['2', '二'],
          ['3', '三'],
          ['4', '四'],
          ['5', '五'],
          ['6', '六'],
        ]);
        const w = map.get(this.getDay()?.toString());
        let prefix = '';
        if (RegExp.$1.length > 2) {
          prefix = '星期';
        } else if (RegExp.$1.length > 1) {
          prefix = '周';
        }
        fmt = fmt.replace(RegExp.$1, prefix + w);
      }

      if (/(q+)/.test(fmt)) {
        // 季度
        const quarterMap = new Map([
          [1, '一'],
          [2, '二'],
          [3, '三'],
          [4, '四'],
        ]);
        const quarterIndex = Math.floor((this.getMonth() + 3) / 3);
        const q = `${quarterMap.get(quarterIndex)}季度`;
        fmt = fmt.replace(RegExp.$1, RegExp.$1.length > 1 ? `第${q}` : q);
      }

      const dateMap = new Map([
        ['M+', this.getMonth() + 1], // 月份
        ['d+', this.getDate()], // 日
        ['H+', this.getHours() % 12 || 12], // 12小时
        ['h+', this.getHours()], // 小时
        ['m+', this.getMinutes()], // 分
        ['s+', this.getSeconds()], // 秒
        ['S', this.getMilliseconds()], // 毫秒
      ]);

      for (const [pattern, value] of dateMap) {
        if (new RegExp(`(${pattern})`).test(fmt)) {
          const str = RegExp.$1.length === 1 ? value.toString() : value.toString().padStart(2, '0');
          fmt = fmt.replace(RegExp.$1, str);
        }
      }
      return fmt;
    };

    /**
     * 基本数据类型数组去重
     * @param isSort 是否排序
     * @returns {T[]}
     */
    window.Array.prototype.unique = function <T>(this: T[], isSort = false): T[] {
      const uniqueArray = [...new Set(this)];
      return isSort && typeof uniqueArray[0] === 'number'
        ? (uniqueArray.sort((a, b) => (a as number) - (b as number)) as T[])
        : uniqueArray;
    };

    /**
     * 对象数组去重
     * @param field 对象键名
     * @returns {T[]}
     */
    window.Array.prototype.uniqueForObject = function <T extends Record<string, any>>(field = 'id'): T[] {
      return Array.from(new Map(this.map((item: T) => [item[field], item])).values()) as T[];
    };

    /**
     * 生成随机字符串
     * @param length 字符串长度，默认为 16
     * @returns {string}
     */
    window.randomString = function (length = 16): string {
      return crypto.randomUUID().replace(/-/g, '').substring(0, length);
    };
  },
};
