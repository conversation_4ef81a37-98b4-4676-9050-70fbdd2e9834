import cloneDeep from 'lodash/cloneDeep';
import { RouteRecordRaw } from 'vue-router';

import { RouteItem } from '@/api/model/permissionModel';
import { RouteMeta } from '@/types/interface';
import { RouteRecordEx } from '@/types/menu';
import {
  BLANK_LAYOUT,
  EXCEPTION_COMPONENT,
  IFRAME,
  LAYOUT,
  PAGE_NOT_FOUND_ROUTE,
  PARENT_LAYOUT,
} from '@/utils/route/constant';

// vite 3+ support dynamic import from node_modules
// const iconsPath = import.meta.glob('../../../node_modules/tdesign-icons-vue-next/esm/components/*.js');

const LayoutMap = new Map<string, () => Promise<typeof import('*.vue')>>();

LayoutMap.set('LAYOUT', LAYOUT);
LayoutMap.set('BLANK', BLANK_LAYOUT);
LayoutMap.set('IFRAME', IFRAME);

let dynamicViewsModules: Record<string, () => Promise<Recordable>>;

// 动态从包内引入单个Icon
async function getMenuIcon(_iconName: string): Promise<string> {
  // const RenderIcon = iconsPath[`../../../node_modules/tdesign-icons-vue-next/esm/components/${_iconName}.js`];

  // const Icon = await RenderIcon();
  // // @ts-ignore
  // return shallowRef(Icon.default);
  return '';
}

// 动态引入路由组件
function asyncImportRoute(routes: RouteItem[] | undefined) {
  dynamicViewsModules = dynamicViewsModules || import.meta.glob('../../pages/**/*.vue');
  if (!routes) return;

  routes.forEach(async (item) => {
    const { component, name } = item;
    const { children } = item;

    if (component) {
      const layoutFound = LayoutMap.get(component.toUpperCase());
      if (layoutFound) {
        item.component = layoutFound;
      } else {
        item.component = dynamicImport(dynamicViewsModules, component);
      }
    } else if (name) {
      item.component = PARENT_LAYOUT();
    }

    if (item.meta.icon) item.meta.icon = await getMenuIcon(item.meta.icon);

    // eslint-disable-next-line no-unused-expressions
    children && asyncImportRoute(children);
  });
}

function dynamicImport(dynamicViewsModules: Record<string, () => Promise<Recordable>>, component: string) {
  const keys = Object.keys(dynamicViewsModules);
  const matchKeys = keys.filter((key) => {
    const k = key.replace('../../pages', '');
    const startFlag = component.startsWith('/');
    const endFlag = component.endsWith('.vue') || component.endsWith('.tsx');
    const startIndex = startFlag ? 0 : 1;
    const lastIndex = endFlag ? k.length : k.lastIndexOf('.');
    return k.substring(startIndex, lastIndex) === component;
  });
  if (matchKeys?.length === 1) {
    const matchKey = matchKeys[0];
    return dynamicViewsModules[matchKey];
  }
  if (matchKeys?.length > 1) {
    throw new Error(
      'Please do not create `.vue` and `.TSX` files with the same file name in the same hierarchical directory under the views folder. This will cause dynamic introduction failure',
    );
  } else {
    console.warn(`Can't find ${component} in pages folder`);
  }
  return EXCEPTION_COMPONENT;
}

// 将背景对象变成路由对象
export function transformObjectToRoute<T = RouteItem>(routeList: RouteItem[]): T[] {
  routeList.forEach(async (route) => {
    const component = route.component as string;

    if (component) {
      if (component.toUpperCase() === 'LAYOUT') {
        route.component = LayoutMap.get(component.toUpperCase());
      } else {
        route.children = [cloneDeep(route)];
        route.component = LAYOUT;
        route.name = `${route.name}Parent`;
        route.path = '';
        route.meta = (route.meta || {}) as RouteMeta;
      }
    } else {
      throw new Error('component is undefined');
    }
    // eslint-disable-next-line no-unused-expressions
    route.children && asyncImportRoute(route.children);
    if (route.meta.icon) route.meta.icon = await getMenuIcon(route.meta.icon);
  });

  return [PAGE_NOT_FOUND_ROUTE, ...routeList] as unknown as T[];
}

// 递归过滤函数
function filterRoutesRecursively(routes: RouteRecordRaw[], map: Record<string, RouteRecordEx>): RouteRecordRaw[] {
  return routes.reduce((acc: RouteRecordRaw[], item) => {
    // 处理当前层级的 children
    const children = item.children || [];
    const filteredChildren = filterRoutesRecursively(children, map);

    // 如果有子级或在 map 中存在该路径，则保留该项
    if (map[item.path] || filteredChildren.length > 0) {
      acc.push({
        ...item,
        children: filteredChildren.length > 0 ? filteredChildren : undefined,
      });
    }

    return acc;
  }, []);
}
/**
 * 匹配权限路由
 */
export async function filterRouter(permissions: RouteRecordEx[], routes: RouteRecordRaw[]): Promise<RouteRecordEx[]> {
  const map: Record<string, RouteRecordEx> = permissions.reduce(
    (res, item) => {
      res[item.url] = item;
      return res;
    },
    {} as { [key: string]: RouteRecordEx },
  );
  const filteredRoutes = routes.map((item) => {
    const children = item.children || [];
    children.forEach((child) => {
      const child1 = map[child.path];
      if (child1) {
        child.meta.menuBlockList = child1.menuBlockList;
      } else {
        // 如果没有匹配到权限，则从children中移除该项
        const index = children.indexOf(child);
        if (index > -1) {
          children.splice(index, 1);
        }
      }
    });
    item.children = children;

    return item;
  });

  const filteredRoutesList = filterRoutesRecursively(filteredRoutes, map);
  // 排序
  filteredRoutesList.sort((val1: Record<string, any>, val2: Record<string, any>) => {
    const a = typeof val1.sort === 'number' ? val1.sort : val1.path.charCodeAt();
    const b = typeof val2.sort === 'number' ? val2.sort : val2.path.charCodeAt();
    return a - b;
  });
  return filteredRoutesList as unknown as RouteRecordEx[];
}
