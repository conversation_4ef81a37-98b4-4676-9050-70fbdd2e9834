// 合同状态枚举
export const CONTRACT_STATUS = {
  FAIL: 0,
  AUDIT_PENDING: 1,
  EXEC_PENDING: 2,
  EXECUTING: 3,
  FINISH: 4,
};

// 合同类型枚举
export const CONTRACT_TYPES = {
  MAIN: 0,
  SUB: 1,
  SUPPLEMENT: 2,
};

// 合同收付类型枚举
export const CONTRACT_PAYMENT_TYPES = {
  PAYMENT: 0,
  RECEIPT: 1,
};

// 标签类型
type TagTheme = 'default' | 'success' | 'primary' | 'warning' | 'danger';
// 通知的优先级对应的标签类型
export const NOTIFICATION_TYPES: Map<string, TagTheme> = new Map([
  ['low', 'primary'],
  ['middle', 'warning'],
  ['high', 'danger'],
]);

// 通用请求头
export enum ContentTypeEnum {
  Json = 'application/json;charset=UTF-8',
  FormURLEncoded = 'application/x-www-form-urlencoded;charset=UTF-8',
  FormData = 'multipart/form-data;charset=UTF-8',
}

// Token name
export const TOKEN_NAME = import.meta.env.VITE_TOKEN_NAME
// 数据字典Code
export const DICTCODE = import.meta.env.VITE_LOGIN_CODE
// Loading 延迟时间（毫秒）
export const LOADING_TIME = 300
// AES密钥
export const AES_KEY = import.meta.env.VITE_AES_KEY
// AES密钥偏移量
export const AES_IV = 'NmQ4YzkOKTg1234='
// 中海发展-组织架构ID
export const ZHFZ_GUID = '20190511182746144-F696-6BE200F84'
// 中海科技-组织架构ID
export const ZHKJ_GUID = '20200327141007738-25AE-FC905FF4B'
// 当前访问地址
export const PATH = 'fullPath'
