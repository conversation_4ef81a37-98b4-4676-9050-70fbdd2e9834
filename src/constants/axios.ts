/**
 * axios 配置文件
 * Author: <PERSON><PERSON>@live.com
 * Date: 2022/9/5 15:44:55
 */
import { IRequestConfig } from '@/api/types/axios';

// 消息/通知配置信息
export const messageOptions = {
  top: '80px', // 偏移量
  class: 'custom-notification', // 自定义类
};

// http异常状态码
export const httpErrorCode: Record<number | string, Record<string, string>> = {
  ECONNABORTED: {
    message: '服务器响应超时',
  },
  0: {
    message: '网络错误',
  },
  400: {
    message: '请求错误',
  },
  401: {
    message: '会话过期，请重新登录',
  },
  403: {
    message: '拒绝访问',
  },
  404: {
    message: '请求地址出错',
  },
  405: {
    message: '请求方法不被允许',
  },
  408: {
    message: '请求超时',
  },
  500: {
    message: '服务器内部错误',
  },
  501: {
    message: '服务未实现',
  },
  502: {
    message: '网关错误',
  },
  503: {
    message: '服务不可用',
  },
  504: {
    message: '网关超时',
  },
  505: {
    message: 'HTTP版本不受支持',
  },
};

export default {
  baseURL: import.meta.env.VITE_API_URL_PREFIX, // Api Host,
  url: '',
  data: {},
  method: 'POST',
  responseType: 'json', // 响应数据类型
  withCredentials: false, // 携带cookie
  timeout: 0, // 超时时间
  putCache: false, // 是否缓存
  showLoading: true, // 是否显示加载动画
} as IRequestConfig<object>;
