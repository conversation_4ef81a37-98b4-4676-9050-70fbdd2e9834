/**
 * Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * Date: 2024-04-23 17:35:01
 */
import { getDict } from '@/utils'
/**
 * 数据字典翻译方法
 * @param {String} value 翻译内容
 * @param {String} dictKey 字典Key
 * @param {Boolean} fullPath 是否为完整字典(含父级)【false: 节点字典，默认true: 完整字典】
 * @return {String | any | string}
 */
export function transDict(value:string, dictKey:string, { fullPath = true } = {}) {
  const key = String(value)
  const dict = getDict()[dictKey]
  if (key === '' || !dict) {
    return value
  }
  if (key === 'null') {
    return ''
  }
  // 节点字典，过滤父级key
  if (!fullPath) {
    const map:any = {}
    for (const [k, v] of Object.entries(dict)) {
      const arr = k && k.split('.') || []
      const newKey = arr.length && arr[arr.length - 1] || k
      map[newKey] = v
    }
    return map[key] || key
  }
  return dict[key] || key

}