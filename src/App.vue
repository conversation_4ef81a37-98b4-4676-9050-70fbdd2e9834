<template>
  <t-config-provider :global-config="getComponentsLocale">
    <router-view :key="locale" :class="[mode]" />
  </t-config-provider>
</template>
<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { useLocale } from '@/locales/useLocale';
import { useSettingStore } from '@/store';

const store = useSettingStore();

const mode = computed(() => {
  return store.displayMode;
});

const { getComponentsLocale, locale } = useLocale();
const timeoutId = ref(null);
const checkTokenValidity = () => {
  const tokenTimeStr = sessionStorage.getItem('tokenTime');
  if (!tokenTimeStr) return;

  const tokenTime = parseInt(tokenTimeStr, 10);
  const now = new Date().getTime();
  const diffMinutes = (now - tokenTime) / (1000 * 60);

  if (diffMinutes >= 20) {
    window.location.reload(); // 超过15分钟，刷新页面
  }
};
const resetTimeout = () => {
  clearTimeout(timeoutId.value);
  sessionStorage.setItem('tokenTime', new Date().getTime().toString());
  // 设置超时时间（如 20 分钟）
  timeoutId.value = setTimeout(
    () => {
      window.location.reload();
    },
    20 * 60 * 1000,
  ); // 20分钟
};
onMounted(() => {
  // 确保在页面加载时正确设置主题
  store.changeMode(store.mode);
  // 监听用户操作
  window.addEventListener('mousemove', resetTimeout);
  window.addEventListener('keydown', resetTimeout);
  window.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      checkTokenValidity();
    }
  });
  resetTimeout(); // 初始化
});

onUnmounted(() => {
  clearTimeout(timeoutId.value);
  window.removeEventListener('mousemove', resetTimeout);
  window.removeEventListener('keydown', resetTimeout);
  window.removeEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      checkTokenValidity();
    }
  });
});
</script>
<style lang="less" scoped>
#nprogress .bar {
  background: var(--td-brand-color) !important;
}
</style>
